/*
 * Copyright 2025 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */
/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: --user-data-dir=C:\Users\<USER>\AppData\Roaming\gui-guider --app-path=D:\GUI_Guider\Gui-Guider\resources\app.asar --no-sandbox --no-zygote --lang=zh-CN --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1755494819112641 --launch-time-ticks=12041316375 --mojo-platform-channel-handle=2956 --field-trial-handle=1712,i,16818944610642394718,15139860505302627447,131072 --disable-features=SpareRendererForSitePerProcess,WinRetrieveSuggestionsOnlyOnDemand /prefetch:1
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_16
#define LV_FONT_MONTSERRATMEDIUM_16 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_16

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xbf, 0xb, 0xf0, 0xaf, 0xa, 0xe0, 0x9e, 0x8,
    0xd0, 0x8c, 0x7, 0xc0, 0x0, 0x0, 0x10, 0xbf,
    0x1a, 0xe0,

    /* U+0022 "\"" */
    0xf5, 0x1f, 0x3f, 0x51, 0xf3, 0xe4, 0xf, 0x3e,
    0x40, 0xf2, 0x72, 0x8, 0x10,

    /* U+0023 "#" */
    0x0, 0x5, 0xc0, 0x3, 0xe0, 0x0, 0x0, 0x7a,
    0x0, 0x5c, 0x0, 0x0, 0x9, 0x80, 0x7, 0xa0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x3, 0x3e,
    0x73, 0x3c, 0x83, 0x30, 0x0, 0xf2, 0x0, 0xc5,
    0x0, 0x0, 0xf, 0x10, 0xe, 0x30, 0x0, 0x2,
    0xf0, 0x0, 0xf2, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x41, 0x38, 0xc3, 0x36, 0xe3, 0x30, 0x0,
    0x89, 0x0, 0x5c, 0x0, 0x0, 0xa, 0x70, 0x7,
    0xa0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x79, 0x0, 0x0, 0x0, 0x0, 0x79,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xe9, 0x20, 0x6,
    0xfc, 0xbc, 0x9e, 0x90, 0xe, 0xb0, 0x79, 0x0,
    0x10, 0xf, 0x80, 0x79, 0x0, 0x0, 0xd, 0xf5,
    0x79, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x50, 0x0,
    0x0, 0x6, 0xcf, 0xfe, 0x40, 0x0, 0x0, 0x79,
    0x5e, 0xf1, 0x0, 0x0, 0x79, 0x5, 0xf3, 0x7,
    0x0, 0x79, 0x7, 0xf1, 0x2f, 0xe9, 0xbc, 0xaf,
    0xa0, 0x3, 0xae, 0xff, 0xd7, 0x0, 0x0, 0x0,
    0x79, 0x0, 0x0, 0x0, 0x0, 0x79, 0x0, 0x0,

    /* U+0025 "%" */
    0x3, 0xde, 0x80, 0x0, 0x5, 0xd0, 0x0, 0xe4,
    0xc, 0x50, 0x1, 0xe3, 0x0, 0x4c, 0x0, 0x5a,
    0x0, 0xa9, 0x0, 0x6, 0xa0, 0x4, 0xc0, 0x4e,
    0x0, 0x0, 0x4c, 0x0, 0x5a, 0xd, 0x50, 0x0,
    0x0, 0xe4, 0x1c, 0x58, 0xa0, 0x0, 0x0, 0x3,
    0xce, 0x73, 0xe1, 0x3c, 0xe9, 0x0, 0x0, 0x0,
    0xd6, 0xe, 0x40, 0xa8, 0x0, 0x0, 0x7c, 0x3,
    0xc0, 0x3, 0xd0, 0x0, 0x2e, 0x20, 0x3c, 0x0,
    0x3d, 0x0, 0xb, 0x70, 0x0, 0xe2, 0x9, 0x80,
    0x6, 0xd0, 0x0, 0x4, 0xdd, 0xa0,

    /* U+0026 "&" */
    0x0, 0x9, 0xef, 0xb1, 0x0, 0x0, 0x9, 0xe4,
    0x3c, 0xa0, 0x0, 0x0, 0xd9, 0x0, 0x7d, 0x0,
    0x0, 0xc, 0xc0, 0x1c, 0xa0, 0x0, 0x0, 0x3f,
    0xae, 0xc1, 0x0, 0x0, 0x1, 0xdf, 0xc0, 0x0,
    0x0, 0x3, 0xeb, 0x8f, 0x70, 0x18, 0x0, 0xdb,
    0x0, 0x7f, 0x65, 0xf0, 0x3f, 0x40, 0x0, 0x8f,
    0xea, 0x3, 0xf7, 0x0, 0x0, 0xcf, 0x70, 0xb,
    0xf9, 0x66, 0xcf, 0xbf, 0x40, 0x8, 0xdf, 0xea,
    0x30, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xf5, 0xf5, 0xe4, 0xe4, 0x72,

    /* U+0028 "(" */
    0x0, 0xda, 0x5, 0xf2, 0xb, 0xc0, 0xf, 0x70,
    0x3f, 0x40, 0x5f, 0x20, 0x6f, 0x10, 0x7f, 0x0,
    0x6f, 0x10, 0x5f, 0x20, 0x3f, 0x40, 0xf, 0x70,
    0xb, 0xc0, 0x5, 0xf2, 0x0, 0xda,

    /* U+0029 ")" */
    0x3f, 0x30, 0xc, 0xb0, 0x6, 0xf1, 0x1, 0xf6,
    0x0, 0xe9, 0x0, 0xbc, 0x0, 0xad, 0x0, 0xae,
    0x0, 0xad, 0x0, 0xbc, 0x0, 0xe9, 0x1, 0xf6,
    0x6, 0xf1, 0xc, 0xb0, 0x3f, 0x30,

    /* U+002A "*" */
    0x0, 0x4a, 0x0, 0x6, 0x74, 0xa4, 0xa0, 0x2b,
    0xff, 0xe5, 0x0, 0x7f, 0xfb, 0x20, 0x7b, 0x6b,
    0x8d, 0x0, 0x4, 0xa0, 0x0, 0x0, 0x13, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x5, 0x10, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x1, 0x1f, 0x51, 0x10,
    0xef, 0xff, 0xff, 0xf3, 0x34, 0x4f, 0x74, 0x40,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0,

    /* U+002D "-" */
    0x1, 0x11, 0x10, 0x1f, 0xff, 0xf3, 0x4, 0x44,
    0x40,

    /* U+002E "." */
    0x3, 0x12, 0xfc, 0x1e, 0x90,

    /* U+002F "/" */
    0x0, 0x0, 0x5, 0xf1, 0x0, 0x0, 0xa, 0xb0,
    0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x5f, 0x10,
    0x0, 0x0, 0xab, 0x0, 0x0, 0x0, 0xf6, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0xa, 0xb0, 0x0,
    0x0, 0xf, 0x60, 0x0, 0x0, 0x4f, 0x10, 0x0,
    0x0, 0xac, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x0, 0x4f, 0x10, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x8, 0xef, 0xc5, 0x0, 0x0, 0xcf, 0xa8,
    0xcf, 0x70, 0x7, 0xf5, 0x0, 0xa, 0xf2, 0xd,
    0xc0, 0x0, 0x1, 0xf8, 0x1f, 0x80, 0x0, 0x0,
    0xdc, 0x3f, 0x60, 0x0, 0x0, 0xbd, 0x3f, 0x60,
    0x0, 0x0, 0xbd, 0x1f, 0x80, 0x0, 0x0, 0xdc,
    0xd, 0xc0, 0x0, 0x1, 0xf8, 0x7, 0xf5, 0x0,
    0xa, 0xf2, 0x0, 0xcf, 0xa8, 0xcf, 0x70, 0x0,
    0x8, 0xef, 0xc5, 0x0,

    /* U+0031 "1" */
    0xef, 0xff, 0x36, 0x7a, 0xf3, 0x0, 0x5f, 0x30,
    0x5, 0xf3, 0x0, 0x5f, 0x30, 0x5, 0xf3, 0x0,
    0x5f, 0x30, 0x5, 0xf3, 0x0, 0x5f, 0x30, 0x5,
    0xf3, 0x0, 0x5f, 0x30, 0x5, 0xf3,

    /* U+0032 "2" */
    0x4, 0xbe, 0xfd, 0x70, 0x7, 0xfd, 0x98, 0xcf,
    0x90, 0x28, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x7, 0xf2, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x0, 0x4f, 0x80, 0x0, 0x0, 0x3f, 0xc0, 0x0,
    0x0, 0x3e, 0xc1, 0x0, 0x0, 0x2e, 0xc1, 0x0,
    0x0, 0x2e, 0xd1, 0x0, 0x0, 0x2e, 0xf8, 0x77,
    0x77, 0x46, 0xff, 0xff, 0xff, 0xfa,

    /* U+0033 "3" */
    0x6f, 0xff, 0xff, 0xff, 0x2, 0x77, 0x77, 0x9f,
    0xb0, 0x0, 0x0, 0xc, 0xe1, 0x0, 0x0, 0x9,
    0xf3, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x0,
    0xdf, 0xe9, 0x10, 0x0, 0x4, 0x59, 0xfd, 0x0,
    0x0, 0x0, 0x6, 0xf4, 0x0, 0x0, 0x0, 0x3f,
    0x64, 0x40, 0x0, 0x8, 0xf3, 0xbf, 0xc9, 0x8c,
    0xfb, 0x0, 0x7c, 0xff, 0xd7, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x1, 0xeb, 0x0, 0x0, 0x0, 0x0,
    0xbe, 0x10, 0x0, 0x0, 0x0, 0x6f, 0x50, 0x0,
    0x0, 0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0xc,
    0xd0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x1, 0xd5,
    0x0, 0x3, 0xf8, 0x0, 0x2f, 0x60, 0x0, 0xed,
    0x22, 0x23, 0xf7, 0x21, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x81, 0x55, 0x55, 0x56, 0xf9, 0x52, 0x0,
    0x0, 0x0, 0x2f, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xf6, 0x0,

    /* U+0035 "5" */
    0x5, 0xff, 0xff, 0xff, 0x0, 0x7f, 0x77, 0x77,
    0x70, 0x8, 0xe0, 0x0, 0x0, 0x0, 0xad, 0x0,
    0x0, 0x0, 0xb, 0xc2, 0x10, 0x0, 0x0, 0xdf,
    0xff, 0xfb, 0x30, 0x4, 0x55, 0x68, 0xff, 0x20,
    0x0, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x0, 0xf,
    0x92, 0x50, 0x0, 0x5, 0xf6, 0x8f, 0xd9, 0x8a,
    0xfd, 0x10, 0x5b, 0xef, 0xe9, 0x10,

    /* U+0036 "6" */
    0x0, 0x5, 0xce, 0xfc, 0x60, 0x0, 0x9f, 0xc8,
    0x8b, 0x70, 0x5, 0xf8, 0x0, 0x0, 0x0, 0xc,
    0xd0, 0x0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x2f, 0x68, 0xef, 0xfa, 0x10, 0x3f, 0xee,
    0x64, 0x8f, 0xd0, 0x2f, 0xf1, 0x0, 0x6, 0xf4,
    0xe, 0xc0, 0x0, 0x2, 0xf6, 0x9, 0xf1, 0x0,
    0x6, 0xf3, 0x1, 0xde, 0x86, 0x9f, 0xb0, 0x0,
    0x19, 0xef, 0xd8, 0x0,

    /* U+0037 "7" */
    0x8f, 0xff, 0xff, 0xff, 0xe8, 0xf7, 0x77, 0x77,
    0xfc, 0x8f, 0x0, 0x0, 0x4f, 0x55, 0x90, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x2, 0xf8, 0x0, 0x0,
    0x0, 0x9f, 0x10, 0x0, 0x0, 0xf, 0xb0, 0x0,
    0x0, 0x6, 0xf4, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0x0, 0x3f, 0x70, 0x0, 0x0, 0xa, 0xf1,
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x6, 0xfc, 0x76,
    0xaf, 0xa0, 0xc, 0xd0, 0x0, 0x9, 0xf1, 0xd,
    0xc0, 0x0, 0x7, 0xf2, 0x7, 0xf7, 0x11, 0x5e,
    0xc0, 0x0, 0xbf, 0xff, 0xfe, 0x10, 0x9, 0xf9,
    0x54, 0x7e, 0xd0, 0x2f, 0x80, 0x0, 0x4, 0xf6,
    0x4f, 0x50, 0x0, 0x0, 0xf8, 0x1f, 0xa0, 0x0,
    0x5, 0xf6, 0x9, 0xfb, 0x76, 0xaf, 0xd0, 0x0,
    0x6c, 0xff, 0xd8, 0x10,

    /* U+0039 "9" */
    0x0, 0x8e, 0xfd, 0x80, 0x0, 0xc, 0xf8, 0x68,
    0xfc, 0x0, 0x5f, 0x50, 0x0, 0x3f, 0x70, 0x8f,
    0x0, 0x0, 0xe, 0xc0, 0x7f, 0x30, 0x0, 0x1f,
    0xf0, 0x1f, 0xd4, 0x13, 0xcf, 0xf1, 0x4, 0xef,
    0xff, 0xa9, 0xf0, 0x0, 0x2, 0x31, 0xa, 0xf0,
    0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x0,
    0x9f, 0x30, 0x9, 0xa7, 0x8d, 0xf7, 0x0, 0x7,
    0xdf, 0xeb, 0x40, 0x0,

    /* U+003A ":" */
    0x1e, 0x92, 0xfc, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x12, 0xfc, 0x1e, 0x90,

    /* U+003B ";" */
    0x1e, 0x92, 0xfc, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xe9, 0x1f, 0xd0, 0xa8, 0xe,
    0x30, 0xa0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x42, 0x0, 0x1, 0x7d, 0xf3,
    0x3, 0x9f, 0xe8, 0x10, 0xbf, 0xb5, 0x0, 0x0,
    0xee, 0x81, 0x0, 0x0, 0x17, 0xdf, 0xb4, 0x0,
    0x0, 0x4, 0xaf, 0xd2, 0x0, 0x0, 0x1, 0x82,

    /* U+003D "=" */
    0xef, 0xff, 0xff, 0xf3, 0x45, 0x55, 0x55, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x10,
    0xef, 0xff, 0xff, 0xf3, 0x34, 0x44, 0x44, 0x40,

    /* U+003E ">" */
    0x50, 0x0, 0x0, 0x0, 0xef, 0x92, 0x0, 0x0,
    0x6, 0xcf, 0xb5, 0x0, 0x0, 0x3, 0x9f, 0xe2,
    0x0, 0x0, 0x6c, 0xf3, 0x2, 0x9e, 0xe9, 0x20,
    0xbf, 0xc6, 0x0, 0x0, 0x93, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x4, 0xbe, 0xfd, 0x70, 0x7, 0xfc, 0x77, 0xbf,
    0xa0, 0x27, 0x0, 0x0, 0xcf, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x0, 0x1, 0xea, 0x0, 0x0,
    0x1, 0xdd, 0x10, 0x0, 0x0, 0xce, 0x10, 0x0,
    0x0, 0x3f, 0x60, 0x0, 0x0, 0x1, 0x30, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x6, 0xf5,
    0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x17, 0xce, 0xfd, 0xb5, 0x0, 0x0,
    0x0, 0x5, 0xfb, 0x53, 0x23, 0x7d, 0xc2, 0x0,
    0x0, 0x6e, 0x40, 0x0, 0x0, 0x0, 0x8e, 0x10,
    0x2, 0xf4, 0x1, 0xae, 0xfa, 0x3f, 0x49, 0xb0,
    0xa, 0x90, 0x1e, 0xe6, 0x5b, 0xef, 0x40, 0xe3,
    0xf, 0x30, 0x8f, 0x10, 0x0, 0xaf, 0x40, 0x98,
    0x1f, 0x0, 0xd9, 0x0, 0x0, 0x3f, 0x40, 0x6a,
    0x3f, 0x0, 0xe8, 0x0, 0x0, 0x1f, 0x40, 0x5c,
    0x1f, 0x0, 0xd9, 0x0, 0x0, 0x3f, 0x40, 0x6a,
    0xf, 0x30, 0x8f, 0x10, 0x0, 0xaf, 0x40, 0x98,
    0xa, 0x90, 0x1e, 0xd6, 0x5a, 0xde, 0xa6, 0xf2,
    0x3, 0xf3, 0x1, 0xaf, 0xfa, 0x16, 0xee, 0x50,
    0x0, 0x6e, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfb, 0x53, 0x23, 0x75, 0x0, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xfd, 0xa3, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x2, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x9e, 0xb0, 0x0, 0x0, 0x0, 0x6, 0xf2, 0x7f,
    0x20, 0x0, 0x0, 0x0, 0xdc, 0x1, 0xf8, 0x0,
    0x0, 0x0, 0x4f, 0x60, 0xb, 0xe0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0x4f, 0x60, 0x0, 0x1, 0xfa,
    0x11, 0x11, 0xed, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0xe, 0xc4, 0x44, 0x44, 0x4f,
    0xa0, 0x6, 0xf4, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0xcd, 0x0, 0x0, 0x0, 0x2, 0xf8,

    /* U+0042 "B" */
    0x5f, 0xff, 0xff, 0xeb, 0x40, 0x5, 0xf8, 0x55,
    0x57, 0xdf, 0x40, 0x5f, 0x40, 0x0, 0x1, 0xfa,
    0x5, 0xf4, 0x0, 0x0, 0xf, 0xa0, 0x5f, 0x51,
    0x11, 0x3a, 0xf4, 0x5, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x5f, 0x74, 0x44, 0x59, 0xfa, 0x5, 0xf4,
    0x0, 0x0, 0x8, 0xf2, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x45, 0xf4, 0x0, 0x0, 0x9, 0xf2, 0x5f,
    0x85, 0x55, 0x6a, 0xfb, 0x5, 0xff, 0xff, 0xff,
    0xd7, 0x0,

    /* U+0043 "C" */
    0x0, 0x2, 0x8d, 0xfe, 0xb4, 0x0, 0x4, 0xff,
    0xb8, 0x9d, 0xf9, 0x2, 0xfd, 0x20, 0x0, 0x8,
    0x50, 0xbf, 0x20, 0x0, 0x0, 0x0, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd2, 0x0, 0x0, 0x85, 0x0,
    0x5f, 0xfb, 0x89, 0xdf, 0x80, 0x0, 0x29, 0xdf,
    0xeb, 0x40,

    /* U+0044 "D" */
    0x5f, 0xff, 0xff, 0xea, 0x30, 0x0, 0x5f, 0x97,
    0x77, 0x9e, 0xf8, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0xaf, 0x60, 0x5f, 0x40, 0x0, 0x0, 0xd, 0xe0,
    0x5f, 0x40, 0x0, 0x0, 0x6, 0xf4, 0x5f, 0x40,
    0x0, 0x0, 0x3, 0xf6, 0x5f, 0x40, 0x0, 0x0,
    0x3, 0xf6, 0x5f, 0x40, 0x0, 0x0, 0x6, 0xf4,
    0x5f, 0x40, 0x0, 0x0, 0xd, 0xe0, 0x5f, 0x40,
    0x0, 0x0, 0xaf, 0x60, 0x5f, 0x97, 0x77, 0x9e,
    0xf8, 0x0, 0x5f, 0xff, 0xff, 0xea, 0x30, 0x0,

    /* U+0045 "E" */
    0x5f, 0xff, 0xff, 0xff, 0x95, 0xf9, 0x77, 0x77,
    0x74, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x5f, 0x51, 0x11, 0x11, 0x5, 0xff,
    0xff, 0xff, 0xe0, 0x5f, 0x74, 0x44, 0x44, 0x5,
    0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x97, 0x77,
    0x77, 0x65, 0xff, 0xff, 0xff, 0xfd,

    /* U+0046 "F" */
    0x5f, 0xff, 0xff, 0xff, 0x95, 0xf9, 0x77, 0x77,
    0x74, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf5,
    0x22, 0x22, 0x10, 0x5f, 0xff, 0xff, 0xfe, 0x5,
    0xf8, 0x55, 0x55, 0x40, 0x5f, 0x40, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x1, 0x8d, 0xfe, 0xb5, 0x0, 0x0, 0x4f,
    0xfb, 0x89, 0xdf, 0xb0, 0x2, 0xfd, 0x20, 0x0,
    0x6, 0x60, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x60, 0x0, 0x0,
    0x9, 0xf0, 0xf, 0xa0, 0x0, 0x0, 0x9, 0xf0,
    0xb, 0xf2, 0x0, 0x0, 0x9, 0xf0, 0x2, 0xfd,
    0x20, 0x0, 0xa, 0xf0, 0x0, 0x4f, 0xfb, 0x89,
    0xdf, 0xc0, 0x0, 0x2, 0x8d, 0xfe, 0xc6, 0x0,

    /* U+0048 "H" */
    0x5f, 0x40, 0x0, 0x0, 0x4f, 0x55, 0xf4, 0x0,
    0x0, 0x4, 0xf5, 0x5f, 0x40, 0x0, 0x0, 0x4f,
    0x55, 0xf4, 0x0, 0x0, 0x4, 0xf5, 0x5f, 0x52,
    0x22, 0x22, 0x5f, 0x55, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x5f, 0x85, 0x55, 0x55, 0x8f, 0x55, 0xf4,
    0x0, 0x0, 0x4, 0xf5, 0x5f, 0x40, 0x0, 0x0,
    0x4f, 0x55, 0xf4, 0x0, 0x0, 0x4, 0xf5, 0x5f,
    0x40, 0x0, 0x0, 0x4f, 0x55, 0xf4, 0x0, 0x0,
    0x4, 0xf5,

    /* U+0049 "I" */
    0x5f, 0x45, 0xf4, 0x5f, 0x45, 0xf4, 0x5f, 0x45,
    0xf4, 0x5f, 0x45, 0xf4, 0x5f, 0x45, 0xf4, 0x5f,
    0x45, 0xf4,

    /* U+004A "J" */
    0x0, 0xff, 0xff, 0xfa, 0x0, 0x77, 0x77, 0xfa,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0xf9, 0x7, 0x20, 0x3, 0xf6,
    0xd, 0xe9, 0x8e, 0xf1, 0x1, 0xae, 0xfb, 0x30,

    /* U+004B "K" */
    0x5f, 0x40, 0x0, 0x2, 0xeb, 0x5, 0xf4, 0x0,
    0x1, 0xec, 0x0, 0x5f, 0x40, 0x1, 0xde, 0x10,
    0x5, 0xf4, 0x0, 0xce, 0x20, 0x0, 0x5f, 0x40,
    0xbf, 0x30, 0x0, 0x5, 0xf4, 0x9f, 0x90, 0x0,
    0x0, 0x5f, 0xcf, 0xef, 0x40, 0x0, 0x5, 0xff,
    0x91, 0xee, 0x10, 0x0, 0x5f, 0xa0, 0x3, 0xfc,
    0x0, 0x5, 0xf4, 0x0, 0x6, 0xf8, 0x0, 0x5f,
    0x40, 0x0, 0x9, 0xf5, 0x5, 0xf4, 0x0, 0x0,
    0xc, 0xf2,

    /* U+004C "L" */
    0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4,
    0x0, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x5,
    0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x97, 0x77,
    0x77, 0x25, 0xff, 0xff, 0xff, 0xf5,

    /* U+004D "M" */
    0x5f, 0x40, 0x0, 0x0, 0x0, 0x1e, 0x95, 0xfc,
    0x0, 0x0, 0x0, 0x8, 0xf9, 0x5f, 0xf5, 0x0,
    0x0, 0x2, 0xff, 0x95, 0xfd, 0xe0, 0x0, 0x0,
    0xae, 0xf9, 0x5f, 0x5f, 0x70, 0x0, 0x3f, 0x5f,
    0x95, 0xf3, 0x8f, 0x10, 0xb, 0xc0, 0xf9, 0x5f,
    0x31, 0xe9, 0x4, 0xf3, 0xf, 0x95, 0xf3, 0x7,
    0xf2, 0xdb, 0x0, 0xf9, 0x5f, 0x30, 0xd, 0xef,
    0x20, 0xf, 0x95, 0xf3, 0x0, 0x5f, 0x90, 0x0,
    0xf9, 0x5f, 0x30, 0x0, 0x71, 0x0, 0xf, 0x95,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0xf9,

    /* U+004E "N" */
    0x5f, 0x50, 0x0, 0x0, 0x4f, 0x55, 0xff, 0x20,
    0x0, 0x4, 0xf5, 0x5f, 0xfd, 0x0, 0x0, 0x4f,
    0x55, 0xfa, 0xf9, 0x0, 0x4, 0xf5, 0x5f, 0x4a,
    0xf5, 0x0, 0x4f, 0x55, 0xf4, 0xd, 0xf2, 0x4,
    0xf5, 0x5f, 0x40, 0x2f, 0xd0, 0x4f, 0x55, 0xf4,
    0x0, 0x6f, 0x94, 0xf5, 0x5f, 0x40, 0x0, 0xaf,
    0xaf, 0x55, 0xf4, 0x0, 0x0, 0xdf, 0xf5, 0x5f,
    0x40, 0x0, 0x2, 0xff, 0x55, 0xf4, 0x0, 0x0,
    0x6, 0xf5,

    /* U+004F "O" */
    0x0, 0x1, 0x8d, 0xfe, 0xb5, 0x0, 0x0, 0x4,
    0xff, 0xb8, 0x9e, 0xfa, 0x0, 0x2, 0xfd, 0x20,
    0x0, 0x8, 0xf9, 0x0, 0xbf, 0x20, 0x0, 0x0,
    0xa, 0xf2, 0xf, 0xa0, 0x0, 0x0, 0x0, 0x3f,
    0x72, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xf9, 0x2f,
    0x60, 0x0, 0x0, 0x0, 0xf, 0x90, 0xfa, 0x0,
    0x0, 0x0, 0x3, 0xf7, 0xb, 0xf2, 0x0, 0x0,
    0x0, 0xaf, 0x20, 0x2f, 0xd2, 0x0, 0x0, 0x8f,
    0x90, 0x0, 0x4f, 0xfb, 0x89, 0xef, 0xa0, 0x0,
    0x0, 0x28, 0xdf, 0xeb, 0x50, 0x0,

    /* U+0050 "P" */
    0x5f, 0xff, 0xff, 0xd7, 0x0, 0x5f, 0x97, 0x78,
    0xbf, 0xc0, 0x5f, 0x40, 0x0, 0x7, 0xf6, 0x5f,
    0x40, 0x0, 0x0, 0xfa, 0x5f, 0x40, 0x0, 0x0,
    0xfa, 0x5f, 0x40, 0x0, 0x3, 0xf8, 0x5f, 0x62,
    0x23, 0x6e, 0xf1, 0x5f, 0xff, 0xff, 0xfd, 0x30,
    0x5f, 0x85, 0x54, 0x20, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x0, 0x5f,
    0x40, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x1, 0x8d, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x4e, 0xfb, 0x89, 0xef, 0xa0, 0x0, 0x2, 0xfd,
    0x20, 0x0, 0x8, 0xf9, 0x0, 0xa, 0xf2, 0x0,
    0x0, 0x0, 0xaf, 0x20, 0xf, 0xa0, 0x0, 0x0,
    0x0, 0x3f, 0x70, 0x2f, 0x60, 0x0, 0x0, 0x0,
    0xf, 0x90, 0x2f, 0x60, 0x0, 0x0, 0x0, 0xf,
    0x90, 0x1f, 0x90, 0x0, 0x0, 0x0, 0x2f, 0x70,
    0xb, 0xf1, 0x0, 0x0, 0x0, 0xaf, 0x20, 0x3,
    0xfc, 0x10, 0x0, 0x7, 0xf9, 0x0, 0x0, 0x6f,
    0xfa, 0x78, 0xdf, 0xb0, 0x0, 0x0, 0x3, 0xae,
    0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xd4, 0x15, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1, 0x31,
    0x0,

    /* U+0052 "R" */
    0x5f, 0xff, 0xff, 0xd7, 0x0, 0x5f, 0x97, 0x78,
    0xbf, 0xc0, 0x5f, 0x40, 0x0, 0x7, 0xf6, 0x5f,
    0x40, 0x0, 0x0, 0xfa, 0x5f, 0x40, 0x0, 0x0,
    0xfa, 0x5f, 0x40, 0x0, 0x3, 0xf8, 0x5f, 0x52,
    0x23, 0x6e, 0xe1, 0x5f, 0xff, 0xff, 0xfc, 0x30,
    0x5f, 0x85, 0x55, 0xf9, 0x0, 0x5f, 0x40, 0x0,
    0x7f, 0x40, 0x5f, 0x40, 0x0, 0xc, 0xe0, 0x5f,
    0x40, 0x0, 0x2, 0xf9,

    /* U+0053 "S" */
    0x0, 0x5c, 0xef, 0xd9, 0x20, 0x7, 0xfc, 0x87,
    0xaf, 0x90, 0xe, 0xc0, 0x0, 0x1, 0x10, 0xf,
    0x80, 0x0, 0x0, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xea, 0x50, 0x0, 0x0, 0x5,
    0xae, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x4e, 0xf1,
    0x0, 0x0, 0x0, 0x5, 0xf3, 0x8, 0x0, 0x0,
    0x8, 0xf2, 0x2f, 0xfa, 0x77, 0xbf, 0xa0, 0x2,
    0x9d, 0xff, 0xc7, 0x0,

    /* U+0054 "T" */
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x67, 0x78, 0xfb,
    0x77, 0x72, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x1,
    0xf7, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xf7, 0x0, 0x0,

    /* U+0055 "U" */
    0x6f, 0x30, 0x0, 0x0, 0x8f, 0x16, 0xf3, 0x0,
    0x0, 0x8, 0xf1, 0x6f, 0x30, 0x0, 0x0, 0x8f,
    0x16, 0xf3, 0x0, 0x0, 0x8, 0xf1, 0x6f, 0x30,
    0x0, 0x0, 0x8f, 0x16, 0xf3, 0x0, 0x0, 0x8,
    0xf1, 0x6f, 0x30, 0x0, 0x0, 0x8f, 0x5, 0xf4,
    0x0, 0x0, 0x9, 0xf0, 0x3f, 0x70, 0x0, 0x0,
    0xcd, 0x0, 0xde, 0x20, 0x0, 0x5f, 0x80, 0x4,
    0xff, 0xa8, 0xbf, 0xd0, 0x0, 0x3, 0xbe, 0xfd,
    0x81, 0x0,

    /* U+0056 "V" */
    0xc, 0xe0, 0x0, 0x0, 0x0, 0x6f, 0x30, 0x6f,
    0x50, 0x0, 0x0, 0xc, 0xc0, 0x0, 0xfb, 0x0,
    0x0, 0x3, 0xf6, 0x0, 0x9, 0xf2, 0x0, 0x0,
    0xae, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x1f, 0x90,
    0x0, 0x0, 0xce, 0x0, 0x7, 0xf2, 0x0, 0x0,
    0x5, 0xf6, 0x0, 0xdb, 0x0, 0x0, 0x0, 0xe,
    0xc0, 0x4f, 0x50, 0x0, 0x0, 0x0, 0x8f, 0x3b,
    0xe0, 0x0, 0x0, 0x0, 0x2, 0xfb, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0x40, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x2,
    0xf5, 0xf, 0x90, 0x0, 0x2, 0xff, 0x40, 0x0,
    0x7, 0xf0, 0xb, 0xe0, 0x0, 0x7, 0xfe, 0x90,
    0x0, 0xc, 0xb0, 0x6, 0xf3, 0x0, 0xc, 0xaa,
    0xe0, 0x0, 0x1f, 0x60, 0x1, 0xf8, 0x0, 0x1f,
    0x54, 0xf3, 0x0, 0x6f, 0x10, 0x0, 0xcd, 0x0,
    0x7f, 0x10, 0xf8, 0x0, 0xcc, 0x0, 0x0, 0x7f,
    0x20, 0xcb, 0x0, 0xad, 0x1, 0xf7, 0x0, 0x0,
    0x2f, 0x71, 0xf6, 0x0, 0x5f, 0x26, 0xf2, 0x0,
    0x0, 0xd, 0xc6, 0xf1, 0x0, 0xf, 0x7b, 0xd0,
    0x0, 0x0, 0x8, 0xfd, 0xc0, 0x0, 0xb, 0xdf,
    0x80, 0x0, 0x0, 0x3, 0xff, 0x70, 0x0, 0x6,
    0xff, 0x30, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0,
    0x1, 0xfe, 0x0, 0x0,

    /* U+0058 "X" */
    0x3f, 0x90, 0x0, 0x0, 0xcd, 0x0, 0x8f, 0x40,
    0x0, 0x7f, 0x30, 0x0, 0xde, 0x10, 0x2f, 0x80,
    0x0, 0x3, 0xfa, 0xc, 0xd0, 0x0, 0x0, 0x7,
    0xfb, 0xf3, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xc0, 0x0, 0x0, 0x0,
    0xbf, 0x7f, 0x70, 0x0, 0x0, 0x6f, 0x60, 0xaf,
    0x20, 0x0, 0x2f, 0xb0, 0x1, 0xed, 0x0, 0xc,
    0xf1, 0x0, 0x4, 0xf8, 0x7, 0xf6, 0x0, 0x0,
    0x9, 0xf3,

    /* U+0059 "Y" */
    0xc, 0xe0, 0x0, 0x0, 0x7, 0xf2, 0x3, 0xf7,
    0x0, 0x0, 0x1f, 0x90, 0x0, 0xaf, 0x10, 0x0,
    0x9e, 0x10, 0x0, 0x1f, 0xa0, 0x2, 0xf6, 0x0,
    0x0, 0x8, 0xf3, 0xb, 0xd0, 0x0, 0x0, 0x0,
    0xec, 0x4f, 0x40, 0x0, 0x0, 0x0, 0x5f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf0, 0x0, 0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0xff, 0xfd, 0x1, 0x77, 0x77,
    0x77, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x1e, 0xc0,
    0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0, 0x0,
    0x8, 0xf5, 0x0, 0x0, 0x0, 0x4, 0xf9, 0x0,
    0x0, 0x0, 0x1, 0xec, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x8f, 0x50, 0x0,
    0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0x1e,
    0xf8, 0x77, 0x77, 0x77, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xf0,

    /* U+005B "[" */
    0x5f, 0xff, 0x5, 0xf7, 0x50, 0x5f, 0x30, 0x5,
    0xf3, 0x0, 0x5f, 0x30, 0x5, 0xf3, 0x0, 0x5f,
    0x30, 0x5, 0xf3, 0x0, 0x5f, 0x30, 0x5, 0xf3,
    0x0, 0x5f, 0x30, 0x5, 0xf3, 0x0, 0x5f, 0x30,
    0x5, 0xf7, 0x50, 0x5f, 0xff, 0x0,

    /* U+005C "\\" */
    0x7e, 0x0, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x0,
    0xc, 0x90, 0x0, 0x0, 0x7, 0xe0, 0x0, 0x0,
    0x2, 0xf4, 0x0, 0x0, 0x0, 0xc9, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0xc, 0x90, 0x0, 0x0, 0x7, 0xe0, 0x0,
    0x0, 0x2, 0xf3, 0x0, 0x0, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x2f, 0x30,
    0x0, 0x0, 0xd, 0x90, 0x0, 0x0, 0x7, 0xe0,

    /* U+005D "]" */
    0xbf, 0xfa, 0x35, 0xea, 0x0, 0xea, 0x0, 0xea,
    0x0, 0xea, 0x0, 0xea, 0x0, 0xea, 0x0, 0xea,
    0x0, 0xea, 0x0, 0xea, 0x0, 0xea, 0x0, 0xea,
    0x0, 0xea, 0x35, 0xea, 0xbf, 0xfa,

    /* U+005E "^" */
    0x0, 0x2f, 0x80, 0x0, 0x0, 0x9d, 0xe0, 0x0,
    0x0, 0xf3, 0xd5, 0x0, 0x6, 0xd0, 0x7b, 0x0,
    0xc, 0x60, 0x1f, 0x20, 0x3f, 0x10, 0xb, 0x80,
    0x9a, 0x0, 0x4, 0xe0,

    /* U+005F "_" */
    0xff, 0xff, 0xff, 0xff, 0x11, 0x11, 0x11, 0x11,

    /* U+0060 "`" */
    0x7, 0xf6, 0x0, 0x3, 0xe7,

    /* U+0061 "a" */
    0x1, 0x9e, 0xfd, 0x80, 0x0, 0xce, 0x87, 0xaf,
    0x90, 0x2, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0,
    0x6, 0xf2, 0x2, 0xbe, 0xff, 0xff, 0x20, 0xec,
    0x42, 0x27, 0xf2, 0x2f, 0x50, 0x0, 0x7f, 0x20,
    0xec, 0x42, 0x7f, 0xf2, 0x2, 0xbf, 0xfb, 0x6f,
    0x20,

    /* U+0062 "b" */
    0x8f, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x2b, 0xfe, 0xb3, 0x0, 0x8f, 0xec, 0x78, 0xef,
    0x30, 0x8f, 0xa0, 0x0, 0x1e, 0xc0, 0x8f, 0x20,
    0x0, 0x7, 0xf1, 0x8f, 0x0, 0x0, 0x5, 0xf3,
    0x8f, 0x20, 0x0, 0x7, 0xf1, 0x8f, 0xa0, 0x0,
    0x1e, 0xd0, 0x8f, 0xec, 0x78, 0xef, 0x30, 0x8e,
    0x2b, 0xfe, 0xb3, 0x0,

    /* U+0063 "c" */
    0x0, 0x3a, 0xef, 0xc4, 0x0, 0x4f, 0xd8, 0x7c,
    0xf4, 0xd, 0xd0, 0x0, 0x7, 0x13, 0xf6, 0x0,
    0x0, 0x0, 0x4f, 0x30, 0x0, 0x0, 0x3, 0xf6,
    0x0, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x6, 0x10,
    0x4f, 0xd7, 0x7c, 0xf4, 0x0, 0x3a, 0xef, 0xc4,
    0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x3b, 0xff, 0xa3, 0xf7, 0x4, 0xfd, 0x87, 0xce,
    0xf7, 0xe, 0xd0, 0x0, 0xb, 0xf7, 0x3f, 0x60,
    0x0, 0x3, 0xf7, 0x4f, 0x30, 0x0, 0x1, 0xf7,
    0x3f, 0x50, 0x0, 0x3, 0xf7, 0xe, 0xc0, 0x0,
    0xa, 0xf7, 0x4, 0xfc, 0x65, 0xbe, 0xf7, 0x0,
    0x3b, 0xff, 0xb2, 0xf7,

    /* U+0065 "e" */
    0x0, 0x3b, 0xfe, 0xa2, 0x0, 0x4, 0xfc, 0x67,
    0xee, 0x20, 0xe, 0xc0, 0x0, 0x1e, 0xa0, 0x3f,
    0x50, 0x0, 0x7, 0xf0, 0x4f, 0xff, 0xff, 0xff,
    0xf1, 0x3f, 0x72, 0x22, 0x22, 0x20, 0xe, 0xc0,
    0x0, 0x2, 0x0, 0x4, 0xfd, 0x87, 0xaf, 0x50,
    0x0, 0x3a, 0xef, 0xd6, 0x0,

    /* U+0066 "f" */
    0x0, 0x5d, 0xfc, 0x0, 0x2f, 0xb5, 0x70, 0x4,
    0xf3, 0x0, 0xc, 0xff, 0xff, 0xa0, 0x48, 0xf7,
    0x53, 0x0, 0x5f, 0x30, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x5f, 0x30, 0x0, 0x5, 0xf3, 0x0, 0x0,
    0x5f, 0x30, 0x0, 0x5, 0xf3, 0x0, 0x0, 0x5f,
    0x30, 0x0,

    /* U+0067 "g" */
    0x0, 0x3b, 0xff, 0xb2, 0xe9, 0x4, 0xfe, 0x87,
    0xcf, 0xf9, 0xe, 0xd1, 0x0, 0xa, 0xf9, 0x3f,
    0x60, 0x0, 0x1, 0xf9, 0x4f, 0x40, 0x0, 0x0,
    0xf9, 0x3f, 0x60, 0x0, 0x1, 0xf9, 0xe, 0xd0,
    0x0, 0x9, 0xf9, 0x4, 0xfd, 0x87, 0xcf, 0xf8,
    0x0, 0x3b, 0xff, 0xb3, 0xf7, 0x0, 0x0, 0x0,
    0x5, 0xf4, 0x9, 0xe9, 0x77, 0xaf, 0xb0, 0x1,
    0x7c, 0xff, 0xd8, 0x0,

    /* U+0068 "h" */
    0x8f, 0x0, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0x0, 0x0, 0x8, 0xf2, 0xbf,
    0xea, 0x10, 0x8f, 0xfb, 0x89, 0xfd, 0x8, 0xf8,
    0x0, 0x6, 0xf4, 0x8f, 0x10, 0x0, 0x1f, 0x78,
    0xf0, 0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0, 0xf,
    0x88, 0xf0, 0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0,
    0xf, 0x88, 0xf0, 0x0, 0x0, 0xf8,

    /* U+0069 "i" */
    0x9e, 0x1a, 0xf2, 0x0, 0x8, 0xf0, 0x8f, 0x8,
    0xf0, 0x8f, 0x8, 0xf0, 0x8f, 0x8, 0xf0, 0x8f,
    0x8, 0xf0,

    /* U+006A "j" */
    0x0, 0x7, 0xe2, 0x0, 0x9, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf1, 0x0, 0x7, 0xf1, 0x0,
    0x7, 0xf1, 0x0, 0x7, 0xf1, 0x0, 0x7, 0xf1,
    0x0, 0x7, 0xf1, 0x0, 0x7, 0xf1, 0x0, 0x7,
    0xf1, 0x0, 0x7, 0xf1, 0x0, 0x8, 0xf0, 0x18,
    0x6e, 0xc0, 0x3e, 0xfc, 0x20,

    /* U+006B "k" */
    0x8f, 0x0, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0x0, 0x0, 0x8, 0xf0, 0x0,
    0x1d, 0xd1, 0x8f, 0x0, 0x1d, 0xe2, 0x8, 0xf0,
    0x1d, 0xe2, 0x0, 0x8f, 0x2d, 0xf3, 0x0, 0x8,
    0xfe, 0xff, 0x70, 0x0, 0x8f, 0xe2, 0xbf, 0x30,
    0x8, 0xf2, 0x1, 0xee, 0x10, 0x8f, 0x0, 0x3,
    0xfb, 0x8, 0xf0, 0x0, 0x7, 0xf7,

    /* U+006C "l" */
    0x8f, 0x8f, 0x8f, 0x8f, 0x8f, 0x8f, 0x8f, 0x8f,
    0x8f, 0x8f, 0x8f, 0x8f,

    /* U+006D "m" */
    0x8e, 0x3c, 0xfe, 0x91, 0x3b, 0xfe, 0xa2, 0x8,
    0xff, 0x96, 0x9f, 0xcf, 0xc6, 0x8f, 0xd0, 0x8f,
    0x70, 0x0, 0x9f, 0xc0, 0x0, 0x5f, 0x58, 0xf1,
    0x0, 0x5, 0xf6, 0x0, 0x1, 0xf7, 0x8f, 0x0,
    0x0, 0x4f, 0x40, 0x0, 0xf, 0x88, 0xf0, 0x0,
    0x4, 0xf4, 0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0,
    0x4f, 0x40, 0x0, 0xf, 0x88, 0xf0, 0x0, 0x4,
    0xf4, 0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0, 0x4f,
    0x40, 0x0, 0xf, 0x80,

    /* U+006E "n" */
    0x8e, 0x3b, 0xfe, 0xa1, 0x8, 0xff, 0xa6, 0x8f,
    0xd0, 0x8f, 0x80, 0x0, 0x6f, 0x48, 0xf1, 0x0,
    0x1, 0xf7, 0x8f, 0x0, 0x0, 0xf, 0x88, 0xf0,
    0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0, 0xf, 0x88,
    0xf0, 0x0, 0x0, 0xf8, 0x8f, 0x0, 0x0, 0xf,
    0x80,

    /* U+006F "o" */
    0x0, 0x3b, 0xef, 0xc4, 0x0, 0x4, 0xfd, 0x87,
    0xcf, 0x60, 0xe, 0xd0, 0x0, 0xb, 0xf1, 0x3f,
    0x60, 0x0, 0x3, 0xf5, 0x4f, 0x30, 0x0, 0x1,
    0xf7, 0x3f, 0x60, 0x0, 0x3, 0xf5, 0xe, 0xd0,
    0x0, 0xb, 0xf1, 0x4, 0xfd, 0x77, 0xcf, 0x60,
    0x0, 0x3b, 0xef, 0xc4, 0x0,

    /* U+0070 "p" */
    0x8e, 0x3b, 0xfe, 0xb3, 0x0, 0x8f, 0xfb, 0x57,
    0xdf, 0x30, 0x8f, 0x90, 0x0, 0xd, 0xc0, 0x8f,
    0x10, 0x0, 0x7, 0xf1, 0x8f, 0x0, 0x0, 0x5,
    0xf3, 0x8f, 0x20, 0x0, 0x7, 0xf1, 0x8f, 0xa0,
    0x0, 0x1e, 0xd0, 0x8f, 0xec, 0x78, 0xef, 0x30,
    0x8f, 0x2b, 0xfe, 0xb3, 0x0, 0x8f, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x3b, 0xff, 0xa2, 0xf7, 0x4, 0xfd, 0x87,
    0xde, 0xf7, 0xe, 0xd0, 0x0, 0xb, 0xf7, 0x3f,
    0x60, 0x0, 0x3, 0xf7, 0x4f, 0x30, 0x0, 0x1,
    0xf7, 0x3f, 0x60, 0x0, 0x3, 0xf7, 0xe, 0xd0,
    0x0, 0xb, 0xf7, 0x4, 0xfd, 0x77, 0xce, 0xf7,
    0x0, 0x3b, 0xff, 0xa3, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0x1, 0xf7,

    /* U+0072 "r" */
    0x8e, 0x2b, 0xf0, 0x8f, 0xed, 0x90, 0x8f, 0xa0,
    0x0, 0x8f, 0x20, 0x0, 0x8f, 0x0, 0x0, 0x8f,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x8f, 0x0, 0x0,
    0x8f, 0x0, 0x0,

    /* U+0073 "s" */
    0x2, 0xae, 0xfd, 0x91, 0x1e, 0xd7, 0x69, 0xd0,
    0x4f, 0x30, 0x0, 0x0, 0x2f, 0xb4, 0x10, 0x0,
    0x6, 0xef, 0xfd, 0x60, 0x0, 0x1, 0x5b, 0xf5,
    0x1, 0x0, 0x0, 0xf7, 0x5f, 0xa7, 0x6b, 0xf3,
    0x19, 0xdf, 0xec, 0x40,

    /* U+0074 "t" */
    0x5, 0xf3, 0x0, 0x0, 0x5f, 0x30, 0x0, 0xcf,
    0xff, 0xfa, 0x4, 0x8f, 0x75, 0x30, 0x5, 0xf3,
    0x0, 0x0, 0x5f, 0x30, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x5f, 0x30, 0x0, 0x4, 0xf4, 0x0, 0x0,
    0x1f, 0xc6, 0x80, 0x0, 0x5d, 0xfc, 0x10,

    /* U+0075 "u" */
    0xae, 0x0, 0x0, 0x2f, 0x5a, 0xe0, 0x0, 0x2,
    0xf5, 0xae, 0x0, 0x0, 0x2f, 0x5a, 0xe0, 0x0,
    0x2, 0xf5, 0xae, 0x0, 0x0, 0x2f, 0x59, 0xf0,
    0x0, 0x4, 0xf5, 0x6f, 0x30, 0x0, 0xaf, 0x51,
    0xee, 0x76, 0xbf, 0xf5, 0x2, 0xbe, 0xfb, 0x3f,
    0x50,

    /* U+0076 "v" */
    0xd, 0xc0, 0x0, 0x0, 0xcb, 0x6, 0xf2, 0x0,
    0x2, 0xf5, 0x0, 0xf9, 0x0, 0x9, 0xe0, 0x0,
    0x9e, 0x0, 0xf, 0x80, 0x0, 0x2f, 0x60, 0x6f,
    0x10, 0x0, 0xc, 0xc0, 0xcb, 0x0, 0x0, 0x5,
    0xf6, 0xf4, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x8f, 0x70, 0x0,

    /* U+0077 "w" */
    0xbb, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x4f, 0x16,
    0xf1, 0x0, 0xe, 0xf6, 0x0, 0x9, 0xc0, 0xf,
    0x60, 0x5, 0xfc, 0xb0, 0x0, 0xf6, 0x0, 0xac,
    0x0, 0xab, 0x5f, 0x10, 0x5f, 0x10, 0x5, 0xf1,
    0xf, 0x50, 0xf7, 0xa, 0xb0, 0x0, 0xf, 0x76,
    0xf0, 0x9, 0xc0, 0xf5, 0x0, 0x0, 0xac, 0xba,
    0x0, 0x4f, 0x8f, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x8, 0xf4, 0x0, 0x0,

    /* U+0078 "x" */
    0x4f, 0x70, 0x0, 0x9f, 0x20, 0x8f, 0x30, 0x5f,
    0x50, 0x0, 0xcd, 0x2e, 0x90, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x4,
    0xfd, 0xe1, 0x0, 0x1, 0xeb, 0xd, 0xc0, 0x0,
    0xbe, 0x10, 0x3f, 0x80, 0x6f, 0x40, 0x0, 0x7f,
    0x40,

    /* U+0079 "y" */
    0xd, 0xc0, 0x0, 0x0, 0xcb, 0x6, 0xf3, 0x0,
    0x2, 0xf4, 0x0, 0xea, 0x0, 0x9, 0xd0, 0x0,
    0x8f, 0x10, 0x1f, 0x70, 0x0, 0x1f, 0x70, 0x7f,
    0x10, 0x0, 0xa, 0xe0, 0xd9, 0x0, 0x0, 0x3,
    0xf9, 0xf2, 0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0,
    0x0, 0x0, 0x6f, 0x40, 0x0, 0x0, 0x0, 0xad,
    0x0, 0x0, 0x1c, 0x79, 0xf5, 0x0, 0x0, 0x1a,
    0xee, 0x70, 0x0, 0x0,

    /* U+007A "z" */
    0x4f, 0xff, 0xff, 0xf9, 0x15, 0x55, 0x5b, 0xf4,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x1, 0xec, 0x0,
    0x0, 0xb, 0xe1, 0x0, 0x0, 0x8f, 0x40, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x1e, 0xe5, 0x55, 0x53,
    0x5f, 0xff, 0xff, 0xfc,

    /* U+007B "{" */
    0x0, 0x2c, 0xf5, 0x0, 0xaf, 0x61, 0x0, 0xcc,
    0x0, 0x0, 0xdb, 0x0, 0x0, 0xdb, 0x0, 0x0,
    0xdb, 0x0, 0x2, 0xea, 0x0, 0x1f, 0xf4, 0x0,
    0x5, 0xfa, 0x0, 0x0, 0xdb, 0x0, 0x0, 0xdb,
    0x0, 0x0, 0xdb, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0xaf, 0x61, 0x0, 0x2c, 0xf5,

    /* U+007C "|" */
    0x5f, 0x15, 0xf1, 0x5f, 0x15, 0xf1, 0x5f, 0x15,
    0xf1, 0x5f, 0x15, 0xf1, 0x5f, 0x15, 0xf1, 0x5f,
    0x15, 0xf1, 0x5f, 0x15, 0xf1, 0x5f, 0x10,

    /* U+007D "}" */
    0xbe, 0x80, 0x3, 0xaf, 0x40, 0x1, 0xf6, 0x0,
    0x1f, 0x70, 0x1, 0xf7, 0x0, 0x1f, 0x70, 0x0,
    0xf9, 0x0, 0xa, 0xfb, 0x0, 0xfb, 0x20, 0x1f,
    0x70, 0x1, 0xf7, 0x0, 0x1f, 0x70, 0x1, 0xf6,
    0x3, 0xaf, 0x40, 0xbe, 0x90, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xe4, 0x0,
    0xb5, 0xc, 0x86, 0xf5, 0x1e, 0x20, 0xf0, 0x3,
    0xef, 0x90, 0x2, 0x0, 0x0, 0x10, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xea, 0x51, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x83, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xb2,
    0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,

    /* U+F00B "" */
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x1b, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0,
    0xbf, 0xff, 0xb0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xfb, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0x0, 0x0, 0x0, 0x3, 0x8, 0xfc, 0x10,
    0x0, 0x1c, 0xf8, 0xff, 0xfc, 0x10, 0x1c, 0xff,
    0xf5, 0xff, 0xfc, 0x2c, 0xff, 0xf5, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x1d, 0xff, 0xfd, 0x10, 0x0, 0x1c,
    0xff, 0xff, 0xfc, 0x10, 0x1c, 0xff, 0xf9, 0xff,
    0xfc, 0x1c, 0xff, 0xf5, 0x5, 0xff, 0xfc, 0xdf,
    0xf5, 0x0, 0x5, 0xff, 0xd1, 0xa4, 0x0, 0x0,
    0x4, 0xa1,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x10, 0x6f, 0xf1, 0x3, 0x10, 0x0,
    0x0, 0x5f, 0xd0, 0x6f, 0xf1, 0x3f, 0xd1, 0x0,
    0x3, 0xff, 0xf1, 0x6f, 0xf1, 0x5f, 0xfd, 0x0,
    0xd, 0xff, 0x40, 0x6f, 0xf1, 0x9, 0xff, 0x70,
    0x4f, 0xf7, 0x0, 0x6f, 0xf1, 0x0, 0xcf, 0xe0,
    0x9f, 0xf0, 0x0, 0x6f, 0xf1, 0x0, 0x5f, 0xf3,
    0xbf, 0xc0, 0x0, 0x6f, 0xf1, 0x0, 0x2f, 0xf5,
    0xbf, 0xc0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf6,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x6, 0xff, 0xd3, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xda, 0xbe, 0xff, 0xf4, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xca, 0x50, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x4, 0xfd, 0xdf, 0xff, 0xff, 0xfd, 0xef, 0x40,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xfe, 0xdf, 0xff, 0xff, 0xfd, 0xdf, 0x40,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x4f,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0x99, 0xff,
    0xbf, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xf6, 0x22,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x2d, 0xfe, 0x35,
    0xff, 0x53, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xc1,
    0x8f, 0xff, 0xf8, 0x2d, 0xfe, 0x40, 0x7f, 0xfa,
    0x1a, 0xff, 0xff, 0xff, 0xa1, 0xaf, 0xf7, 0xcf,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x28, 0xfc,
    0x14, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x41, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x8f,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x6f, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x1b, 0xb1, 0xcf, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xc2, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F01C "" */
    0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x1e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F021 "" */
    0x0, 0x0, 0x6, 0xbd, 0xda, 0x50, 0x2, 0xff,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x42, 0xff,
    0x0, 0x7f, 0xff, 0xa7, 0x7b, 0xff, 0xf9, 0xff,
    0x5, 0xff, 0xc1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xe, 0xfc, 0x0, 0x0, 0x2, 0x22, 0xdf, 0xff,
    0x5f, 0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x8f, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0xf4,
    0xff, 0xfd, 0x22, 0x20, 0x0, 0x0, 0xcf, 0xe0,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x2c, 0xff, 0x40,
    0xff, 0x9f, 0xff, 0xb7, 0x6a, 0xff, 0xf7, 0x0,
    0xff, 0x24, 0xdf, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0x20, 0x5, 0xac, 0xdb, 0x60, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x8f, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x1, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x0, 0xae,
    0xff, 0xff, 0xff, 0xff, 0x5, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0x2, 0x60, 0x0, 0x0, 0x9f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x3, 0xee, 0x10, 0x0, 0x0, 0x8, 0xff, 0x0,
    0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x5, 0xfc, 0x7, 0xf4, 0xdf, 0xff, 0xff,
    0xff, 0x2, 0x50, 0x5f, 0x60, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xd, 0xc0, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x6, 0xf7, 0xd,
    0xc0, 0xad, 0xdf, 0xff, 0xff, 0xff, 0x2, 0x50,
    0x5f, 0x60, 0xe9, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0x5, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x8, 0xff,
    0x0, 0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x2, 0xee, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,

    /* U+F03E "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xfe, 0x22, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe2, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x4e, 0xfe, 0x20, 0x0, 0x2, 0xff,
    0xff, 0xe2, 0x2, 0xc2, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F043 "" */
    0x0, 0x0, 0x4e, 0x40, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x30, 0x0, 0xc, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xf2, 0xbf, 0xff,
    0xff, 0xfe, 0x9f, 0xa1, 0xbf, 0xff, 0xff, 0x92,
    0xff, 0xa2, 0x2f, 0xff, 0xf2, 0x4, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x2, 0x9e, 0xfe, 0x92, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x1, 0xcc, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff,
    0x40, 0x3, 0xef, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x45, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x3, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x2e, 0xff, 0xff, 0x30,
    0x0, 0x1, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x10,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xf7, 0x0, 0x7f, 0xff,
    0xf7,

    /* U+F04D "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x10, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xff,
    0xfe, 0x30, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0x44, 0xff, 0xff,
    0xff, 0xf3, 0x4, 0xff, 0xff, 0xfe, 0x30, 0x4,
    0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xcc, 0x10,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x1a, 0x40, 0x0, 0x0, 0x1,
    0xdf, 0xf0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0x1d, 0xff, 0xa0,
    0x0, 0x1, 0xdf, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x1b, 0x50,

    /* U+F054 "" */
    0x4, 0xa1, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0xaf, 0xfd, 0x10, 0x0, 0x0, 0xa, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8,
    0xff, 0xd1, 0x0, 0x0, 0xf, 0xfd, 0x10, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x48, 0x88, 0x8c, 0xff, 0xc8,
    0x88, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0x88, 0x8c, 0xff, 0xc8, 0x88, 0x84, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0,

    /* U+F068 "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F06E "" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf, 0xfd,
    0x40, 0x0, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x9e,
    0x80, 0x4f, 0xff, 0x70, 0x4f, 0xff, 0xc0, 0x0,
    0xaf, 0xf8, 0xc, 0xff, 0xf4, 0xdf, 0xff, 0x80,
    0x9a, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0xdf, 0xff,
    0x80, 0xef, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0x4f,
    0xff, 0xc0, 0x8f, 0xff, 0xf8, 0xc, 0xff, 0xf4,
    0x7, 0xff, 0xf4, 0x8, 0xee, 0x80, 0x4f, 0xff,
    0x70, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4, 0xef,
    0xf8, 0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xda, 0x50, 0x0, 0x0,

    /* U+F070 "" */
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x80, 0x49,
    0xdf, 0xfd, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xd8, 0x8c, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x4e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x69, 0xe8,
    0x4, 0xff, 0xf7, 0x0, 0x4, 0xe3, 0x0, 0x9f,
    0xfe, 0xff, 0x80, 0xcf, 0xff, 0x40, 0xd, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xe0, 0x8f, 0xff, 0xd0,
    0xd, 0xff, 0xf7, 0x0, 0x2d, 0xff, 0xe0, 0x8f,
    0xff, 0xd0, 0x4, 0xff, 0xfc, 0x0, 0x0, 0xaf,
    0xf8, 0xcf, 0xff, 0x30, 0x0, 0x7f, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x3e, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xc8, 0x82, 0x1, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfc,
    0x10, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd8, 0x8d,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0xb, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xc0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf9, 0x9f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xe2, 0x2e, 0xff, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xe3, 0x3e,
    0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0x78, 0x8e, 0xff, 0x15, 0xff, 0xe8, 0xff, 0xe2,
    0x0, 0x2, 0xe5, 0x4f, 0xfe, 0x20, 0xfe, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf3, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x31, 0x0, 0x52, 0x0,
    0x0, 0x2, 0xef, 0xf4, 0x5e, 0x20, 0xfe, 0x20,
    0x78, 0x8e, 0xff, 0x51, 0xff, 0xe8, 0xff, 0xe2,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0x99,
    0xff, 0xd1, 0x0, 0x1, 0xdf, 0xf9, 0x0, 0x9f,
    0xfd, 0x10, 0x1d, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xd1, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x1d, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xd1, 0x1, 0xdf, 0xf9, 0x0,
    0x9f, 0xfd, 0x10, 0x0, 0x1d, 0xff, 0x99, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1d, 0xff,
    0xff, 0xd1, 0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x6b, 0x1f, 0xf1, 0xb6, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6b, 0x1f,
    0xf1, 0xb6, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x8f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf0, 0xdf, 0xfd, 0xf, 0xff, 0xfd,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xea,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x2, 0x8f,
    0xf3, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0xa, 0xff,
    0xff, 0xe4, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x8, 0xee, 0x80, 0x0, 0x0, 0x6, 0x61, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0xef,
    0x33, 0xfe, 0x0, 0x2e, 0xff, 0xf3, 0xe, 0xf3,
    0x3f, 0xe0, 0x2e, 0xff, 0xf3, 0x0, 0x8f, 0xff,
    0xff, 0x6e, 0xff, 0xf3, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xf6, 0xef,
    0xff, 0x30, 0x0, 0xef, 0x33, 0xfe, 0x2, 0xef,
    0xff, 0x30, 0xe, 0xf3, 0x3f, 0xe0, 0x2, 0xef,
    0xff, 0x30, 0x8f, 0xff, 0xf8, 0x0, 0x2, 0xdf,
    0xfd, 0x0, 0x8e, 0xe8, 0x0, 0x0, 0x0, 0x66,
    0x10,

    /* U+F0C5 "" */
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xd, 0x20, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf, 0xe2, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf, 0xfd, 0xdf, 0xf0, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,

    /* U+F0C7 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xe2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x11, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x11, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+F0E0 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xd2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2d,
    0xff, 0x62, 0xcf, 0xff, 0xff, 0xfc, 0x26, 0xff,
    0xff, 0xfa, 0x18, 0xff, 0xff, 0x81, 0xaf, 0xff,
    0xff, 0xff, 0xe3, 0x4d, 0xd4, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x18, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F0E7 "" */
    0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x4, 0xee, 0x40, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x99, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0x99, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xd, 0xff, 0xff,
    0xd, 0x20, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf,
    0xe2, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf, 0xfd,
    0xff, 0xff, 0xf, 0xff, 0xff, 0x20, 0x0, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfd,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0x40, 0x0, 0x0,

    /* U+F11C "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0, 0xf0,
    0xf, 0x0, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff, 0xf8,
    0x8, 0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0xff, 0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xdf, 0xff, 0xff, 0xf0, 0xd2, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0xfe, 0x20, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xe2, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x4, 0xdf,
    0xff, 0xfc, 0xa8, 0x8a, 0xcf, 0xff, 0xfd, 0x40,
    0x6f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xf6, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x1a, 0x30, 0x0, 0x5a,
    0xdf, 0xfd, 0xa5, 0x0, 0x3, 0xa1, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xa8, 0x8a, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0, 0x0,
    0x7, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F241 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F242 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F243 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F244 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x29, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0x80, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0xdf,
    0xff, 0x77, 0xf7, 0x55, 0x55, 0x55, 0x55, 0x8f,
    0xd3, 0xf, 0xff, 0xfd, 0xcc, 0xdf, 0xdc, 0xcc,
    0xcc, 0xcd, 0xff, 0xb0, 0x8f, 0xfe, 0x10, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x46,
    0x10, 0x0, 0x1, 0xf2, 0x2, 0x33, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb1, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0, 0x2, 0xef,
    0xfb, 0xef, 0xff, 0x30, 0xd, 0xff, 0xfa, 0x2e,
    0xff, 0xe0, 0x4f, 0xff, 0xfa, 0x3, 0xff, 0xf5,
    0x9f, 0xfa, 0xfa, 0x35, 0x4f, 0xfa, 0xcf, 0xc0,
    0x8a, 0x3d, 0xb, 0xfd, 0xef, 0xfb, 0x3, 0x12,
    0x8f, 0xfe, 0xff, 0xff, 0xb0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x8, 0xff, 0xff, 0xef, 0xfd,
    0x11, 0x10, 0x9f, 0xff, 0xdf, 0xd1, 0x59, 0x3b,
    0xb, 0xfd, 0xaf, 0xd7, 0xfa, 0x38, 0x1d, 0xfb,
    0x5f, 0xff, 0xfa, 0x1, 0xdf, 0xf7, 0xd, 0xff,
    0xfa, 0x1d, 0xff, 0xf1, 0x3, 0xef, 0xfc, 0xdf,
    0xff, 0x50, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x7f, 0xff, 0xf7, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9, 0x9f,
    0xf0, 0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0,
    0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8,
    0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9,
    0x9f, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x1d,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa,
    0x1d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xfa, 0x1d, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0xdb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfa, 0xef, 0xfe, 0xaf, 0xff, 0xff, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x2, 0xef, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x2, 0xef, 0xff, 0xff, 0x1d, 0xff,
    0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa,
    0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0xef,
    0xfe, 0xaf, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F7C2 "" */
    0x0, 0x8, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xfe, 0x8, 0xf8, 0xf, 0xb,
    0x40, 0xff, 0x8f, 0xf8, 0xf, 0xb, 0x40, 0xff,
    0xff, 0xf8, 0xf, 0xb, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x10, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x11, 0xcf, 0xff, 0x77, 0x77, 0x77,
    0x77, 0xbf, 0xf1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 69, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 69, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 18, .adv_w = 100, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 31, .adv_w = 180, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 97, .adv_w = 159, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 177, .adv_w = 216, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 255, .adv_w = 176, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 327, .adv_w = 54, .box_w = 2, .box_h = 5, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 332, .adv_w = 86, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 362, .adv_w = 87, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 392, .adv_w = 102, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 417, .adv_w = 149, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 449, .adv_w = 98, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 458, .adv_w = 58, .box_w = 3, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 463, .adv_w = 90, .box_w = 8, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 527, .adv_w = 171, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 587, .adv_w = 95, .box_w = 5, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 617, .adv_w = 147, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 671, .adv_w = 146, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 725, .adv_w = 171, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 791, .adv_w = 147, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 845, .adv_w = 158, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 905, .adv_w = 153, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 959, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1019, .adv_w = 158, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1079, .adv_w = 58, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1093, .adv_w = 58, .box_w = 3, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1111, .adv_w = 149, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1143, .adv_w = 149, .box_w = 8, .box_h = 6, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1167, .adv_w = 149, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1199, .adv_w = 147, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1253, .adv_w = 265, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1373, .adv_w = 187, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1451, .adv_w = 194, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1517, .adv_w = 185, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1583, .adv_w = 211, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1655, .adv_w = 172, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1709, .adv_w = 163, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1763, .adv_w = 198, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1835, .adv_w = 208, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1901, .adv_w = 79, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1919, .adv_w = 131, .box_w = 8, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1967, .adv_w = 184, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2033, .adv_w = 152, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2087, .adv_w = 244, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2165, .adv_w = 208, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2231, .adv_w = 215, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2309, .adv_w = 185, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2369, .adv_w = 215, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2474, .adv_w = 186, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2534, .adv_w = 159, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2594, .adv_w = 150, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2654, .adv_w = 202, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2720, .adv_w = 182, .box_w = 13, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2798, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2906, .adv_w = 172, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2972, .adv_w = 166, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3044, .adv_w = 168, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3110, .adv_w = 85, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3148, .adv_w = 90, .box_w = 8, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3212, .adv_w = 85, .box_w = 4, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3242, .adv_w = 149, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 3270, .adv_w = 128, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3278, .adv_w = 154, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 3283, .adv_w = 153, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3324, .adv_w = 175, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3384, .adv_w = 146, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3425, .adv_w = 175, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3485, .adv_w = 157, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3530, .adv_w = 90, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3572, .adv_w = 177, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3632, .adv_w = 174, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3686, .adv_w = 71, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3704, .adv_w = 73, .box_w = 6, .box_h = 15, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 3749, .adv_w = 158, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3803, .adv_w = 71, .box_w = 2, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3815, .adv_w = 271, .box_w = 15, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3883, .adv_w = 174, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3924, .adv_w = 163, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3969, .adv_w = 175, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4029, .adv_w = 175, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4089, .adv_w = 105, .box_w = 6, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4116, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4152, .adv_w = 106, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4191, .adv_w = 173, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4232, .adv_w = 143, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4277, .adv_w = 230, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4345, .adv_w = 141, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4386, .adv_w = 143, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 4446, .adv_w = 133, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4482, .adv_w = 90, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4527, .adv_w = 77, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4550, .adv_w = 90, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4588, .adv_w = 149, .box_w = 9, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 4611, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4747, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4843, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4955, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5051, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5117, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5245, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5373, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5499, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5627, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5735, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5863, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5919, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6003, .adv_w = 288, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6147, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6243, .adv_w = 176, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6331, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 6411, .adv_w = 224, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6537, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6642, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6740, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 6820, .adv_w = 224, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 6932, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7002, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7072, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7170, .adv_w = 224, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 7198, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7306, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7466, .adv_w = 288, .box_w = 20, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7626, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7754, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 7824, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 7894, .adv_w = 320, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8034, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8130, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8258, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8403, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8508, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8620, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8718, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8816, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8912, .adv_w = 160, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9008, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9120, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9232, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9340, .adv_w = 256, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9502, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9598, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9748, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 9848, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 9948, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 10048, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 10148, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 10248, .adv_w = 320, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10395, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10491, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10603, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10748, .adv_w = 320, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10868, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10964, .adv_w = 258, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 12, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 45, .range_length = 82, .glyph_id_start = 13,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 95,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 0, 13, 14, 15, 16, 17, 18,
    19, 12, 20, 20, 0, 0, 0, 21,
    22, 23, 24, 25, 22, 26, 27, 28,
    29, 29, 30, 31, 32, 29, 29, 22,
    33, 34, 35, 3, 36, 30, 37, 37,
    38, 39, 40, 41, 42, 43, 0, 44,
    0, 45, 46, 47, 48, 49, 50, 51,
    45, 52, 52, 53, 48, 45, 45, 46,
    46, 54, 55, 56, 57, 51, 58, 58,
    59, 58, 60, 41, 0, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 13, 14, 15, 16, 17, 12, 18,
    19, 20, 21, 21, 0, 0, 0, 22,
    23, 24, 25, 23, 25, 25, 25, 23,
    25, 25, 26, 25, 25, 25, 25, 23,
    25, 23, 25, 3, 27, 28, 29, 29,
    30, 31, 32, 33, 34, 35, 0, 36,
    0, 37, 38, 39, 39, 39, 0, 39,
    38, 40, 41, 38, 38, 42, 42, 39,
    42, 39, 42, 43, 44, 45, 46, 46,
    47, 46, 48, 0, 0, 35, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 12, 0, 7, -6, 0, 0, 0,
    0, -14, -15, 2, 12, 6, 4, -10,
    2, 13, 1, 11, 3, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 15, 2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, 0, 0, 0, 0, -5,
    4, 5, 0, 0, -3, 0, -2, 3,
    0, -3, 0, -3, -1, -5, 0, 0,
    0, 0, -3, 0, 0, -3, -4, 0,
    0, -3, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -7, 0, -31, 0, 0, -5, 0,
    5, 8, 0, 0, -5, 3, 3, 8,
    5, -4, 5, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, -13, 0, -10, -2, 0, 0, 0,
    0, 1, 10, 0, -8, -2, -1, 1,
    0, -4, 0, 0, -2, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, -2, 10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 3, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 5, 3, 8, -3, 0, 0, 5,
    -3, -8, -35, 2, 7, 5, 1, -3,
    0, 9, 0, 8, 0, 8, 0, -24,
    0, -3, 8, 0, 8, -3, 5, 3,
    0, 0, 1, -3, 0, 0, -4, 20,
    0, 20, 0, 8, 0, 11, 3, 4,
    0, 0, 0, -9, 0, 0, 0, 0,
    1, -2, 0, 2, -5, -3, -5, 2,
    0, -3, 0, 0, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -14, 0, -16, 0, 0, 0, 0,
    -2, 0, 25, -3, -3, 3, 3, -2,
    0, -3, 3, 0, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 15, 0, 0, -9, 0, 8, 0,
    -17, -25, -17, -5, 8, 0, 0, -17,
    0, 3, -6, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 8, -31, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 2,
    2, -3, -5, 0, -1, -1, -3, 0,
    0, -2, 0, 0, 0, -5, 0, -2,
    0, -6, -5, 0, -6, -8, -8, -5,
    0, -5, 0, -5, 0, 0, 0, 0,
    -2, 0, 0, 3, 0, 2, -3, 0,
    0, 0, 0, 3, -2, 0, 0, 0,
    -2, 3, 3, -1, 0, 0, 0, -5,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 3, -2, 0, -3, 0, -4, 0,
    0, -2, 0, 8, 0, 0, -3, 0,
    0, 0, 0, 0, -1, 1, -2, -2,
    0, -3, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -3, -3, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    0, -8, -2, -8, 5, 0, 0, -5,
    3, 5, 7, 0, -6, -1, -3, 0,
    -1, -12, 3, -2, 2, -14, 3, 0,
    0, 1, -13, 0, -14, -2, -22, -2,
    0, -13, 0, 5, 7, 0, 3, 0,
    0, 0, 0, 1, 0, -5, -3, 0,
    0, 0, 0, -3, 0, 0, 0, -3,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -3, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, -2, -3, -2, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -2, 0, -5, 3, 0, 0, -3,
    1, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -3, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -3, -4, 0,
    0, 8, -2, 1, -8, 0, 0, 7,
    -13, -13, -11, -5, 3, 0, -2, -17,
    -5, 0, -5, 0, -5, 4, -5, -16,
    0, -7, 0, 0, 1, -1, 2, -2,
    0, 3, 0, -8, -10, 0, -13, -6,
    -5, -6, -8, -3, -7, -1, -5, -7,
    0, 1, 0, -3, 0, 0, 0, 2,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, -1,
    0, -1, -3, 0, -4, -6, -6, -1,
    0, -8, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 1, -2, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -5, 0, 0, 0,
    0, -13, -8, 0, 0, 0, -4, -13,
    0, 0, -3, 3, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 3, 0,
    2, -5, -5, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, -8, 0, -3,
    0, -4, -3, 0, -6, -6, -8, -2,
    0, -5, 0, -8, 0, 0, 0, 0,
    20, 0, 0, 1, 0, 0, -3, 0,
    0, -11, 0, 0, 0, 0, 0, -24,
    -5, 8, 8, -2, -11, 0, 3, -4,
    0, -13, -1, -3, 3, -18, -3, 3,
    0, 4, -9, -4, -9, -8, -11, 0,
    0, -15, 0, 15, 0, 0, -1, 0,
    0, 0, -1, -1, -3, -7, -8, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -1, -3, -4, 0,
    0, -5, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -5, 0, 0, 5,
    -1, 3, 0, -6, 3, -2, -1, -7,
    -3, 0, -3, -3, -2, 0, -4, -4,
    0, 0, -2, -1, -2, -4, -3, 0,
    0, -3, 0, 3, -2, 0, -6, 0,
    0, 0, -5, 0, -4, 0, -4, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 3, 0, -4, 0, -2, -3, -8,
    -2, -2, -2, -1, -2, -3, -1, 0,
    0, 0, 0, 0, -3, -2, -2, 0,
    0, 0, 0, 3, -2, 0, -2, 0,
    0, 0, -2, -3, -2, -2, -3, -2,
    2, 10, -1, 0, -7, 0, -2, 5,
    0, -3, -11, -3, 4, 0, 0, -12,
    -4, 3, -4, 2, 0, -2, -2, -8,
    0, -4, 1, 0, 0, -4, 0, 0,
    0, 3, 3, -5, -5, 0, -4, -3,
    -4, -3, -3, 0, -4, 1, -5, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, -3, 0, 0, -3, -3, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, 0,
    0, 0, -4, 0, -5, 0, 0, 0,
    -8, 0, 2, -6, 5, 1, -2, -12,
    0, 0, -6, -3, 0, -10, -6, -7,
    0, 0, -11, -3, -10, -10, -12, 0,
    -7, 0, 2, 17, -3, 0, -6, -3,
    -1, -3, -4, -7, -5, -9, -10, -6,
    0, 0, -2, 0, 1, 0, 0, -18,
    -2, 8, 6, -6, -9, 0, 1, -8,
    0, -13, -2, -3, 5, -24, -3, 1,
    0, 0, -17, -3, -13, -3, -19, 0,
    0, -18, 0, 15, 1, 0, -2, 0,
    0, 0, 0, -1, -2, -10, -2, 0,
    0, 0, 0, 0, -8, 0, -2, 0,
    -1, -7, -12, 0, 0, -1, -4, -8,
    -3, 0, -2, 0, 0, 0, 0, -12,
    -3, -8, -8, -2, -4, -6, -3, -4,
    0, -5, -2, -8, -4, 0, -3, -5,
    -3, -5, 0, 1, 0, -2, -8, 0,
    0, -5, 0, 0, 0, 0, 3, 0,
    2, -5, 10, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, -8, 0, -3,
    0, -4, -3, 0, -6, -6, -8, -2,
    0, -5, 2, 10, 0, 0, 0, 0,
    20, 0, 0, 1, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, -5,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -3, -3, 0, 0, -5, -3, 0,
    0, -5, 0, 4, -1, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    5, 2, -2, 0, -8, -4, 0, 8,
    -8, -8, -5, -5, 10, 5, 3, -22,
    -2, 5, -3, 0, -3, 3, -3, -9,
    0, -3, 3, -3, -2, -8, -2, 0,
    0, 8, 5, 0, -7, 0, -14, -3,
    7, -3, -10, 1, -3, -8, -8, -3,
    3, 0, -4, 0, -7, 0, 2, 8,
    -6, -9, -10, -6, 8, 0, 1, -19,
    -2, 3, -4, -2, -6, 0, -6, -9,
    -4, -4, -2, 0, 0, -6, -5, -3,
    0, 8, 6, -3, -14, 0, -14, -4,
    0, -9, -15, -1, -8, -4, -8, -7,
    0, 0, -3, 0, -5, -2, 0, -3,
    -5, 0, 4, -8, 3, 0, 0, -14,
    0, -3, -6, -4, -2, -8, -6, -8,
    -6, 0, -8, -3, -6, -5, -8, -3,
    0, 0, 1, 12, -4, 0, -8, -3,
    0, -3, -5, -6, -7, -7, -10, -3,
    5, 0, -4, 0, -13, -3, 2, 5,
    -8, -9, -5, -8, 8, -3, 1, -24,
    -5, 5, -6, -4, -9, 0, -8, -11,
    -3, -3, -2, -3, -5, -8, -1, 0,
    0, 8, 7, -2, -17, 0, -15, -6,
    6, -10, -17, -5, -9, -11, -13, -8,
    0, 0, 0, 0, -3, 0, 0, 3,
    -3, 5, 2, -5, 5, 0, 0, -8,
    -1, 0, -1, 0, 1, 1, -2, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 2, 8, 1, 0, -3, 0,
    0, 0, 0, -2, -2, -3, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 10, 0, 5, 1, 1, -3,
    0, 5, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 0, 7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, -3, 4, 0, 8, 0,
    0, 25, 3, -5, -5, 3, 3, -2,
    1, -13, 0, 0, 12, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 10, 36, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, 0, -5, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -7, 0, 0, 1, 0,
    0, 3, 33, -5, -2, 8, 7, -7,
    3, 0, 0, 3, 3, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -33, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, -7,
    0, 0, 0, 0, -6, -1, 0, 0,
    0, -6, 0, -3, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -4, 0, -7, 0, 0, 0, -4,
    3, -3, 0, 0, -7, -3, -6, 0,
    0, -7, 0, -3, 0, -12, 0, -3,
    0, 0, -21, -5, -10, -3, -9, 0,
    0, -17, 0, -7, -1, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -5, -2,
    0, 0, 0, 0, -6, 0, -6, 3,
    -3, 5, 0, -2, -6, -2, -4, -5,
    0, -3, -1, -2, 2, -7, -1, 0,
    0, 0, -23, -2, -4, 0, -6, 0,
    -2, -12, -2, 0, 0, -2, -2, 0,
    0, 0, 0, 2, 0, -2, -4, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, -6, 0, -2, 0, 0, 0, -5,
    3, 0, 0, 0, -7, -3, -5, 0,
    0, -7, 0, -3, 0, -12, 0, 0,
    0, 0, -25, 0, -5, -9, -13, 0,
    0, -17, 0, -2, -4, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -4, -1,
    1, 0, 0, 4, -3, 0, 8, 13,
    -3, -3, -8, 3, 13, 4, 6, -7,
    3, 11, 3, 7, 6, 7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 16, 12, -5, -3, 0, -2, 20,
    11, 20, 0, 0, 0, 3, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -22, -3, -2, -10, -13, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -22, -3, -2, -10, -13, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -6, 3, 0, -3, 2, 5, 3, -8,
    0, -1, -2, 3, 0, 2, 0, 0,
    0, 0, -6, 0, -2, -2, -5, 0,
    -2, -10, 0, 16, -3, 0, -6, -2,
    0, -2, -4, 0, -3, -7, -5, -3,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -22, -3, -2, -10, -13, 0,
    0, -17, 0, 0, 0, 0, 0, 0,
    13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -8, -3, -2, 8,
    -2, -3, -10, 1, -2, 1, -2, -7,
    1, 6, 1, 2, 1, 2, -6, -10,
    -3, 0, -10, -5, -7, -11, -10, 0,
    -4, -5, -3, -3, -2, -2, -3, -2,
    0, -2, -1, 4, 0, 4, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -3, -3, 0,
    0, -7, 0, -1, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 0, -2, 0, 0, -4,
    -3, 3, 0, -4, -5, -2, 0, -7,
    -2, -6, -2, -3, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -17, 0, 8, 0, 0, -5, 0,
    0, 0, 0, -3, 0, -3, 0, 0,
    0, 0, -2, 0, -6, 0, 0, 11,
    -3, -8, -8, 2, 3, 3, -1, -7,
    2, 4, 2, 8, 2, 8, -2, -7,
    0, 0, -10, 0, 0, -8, -7, 0,
    0, -5, 0, -3, -4, 0, -4, 0,
    -4, 0, -2, 4, 0, -2, -8, -3,
    0, 0, -2, 0, -5, 0, 0, 3,
    -6, 0, 3, -3, 2, 0, 0, -8,
    0, -2, -1, 0, -3, 3, -2, 0,
    0, 0, -10, -3, -6, 0, -8, 0,
    0, -12, 0, 9, -3, 0, -5, 0,
    2, 0, -3, 0, -3, -8, 0, -3,
    0, 0, 0, 0, -2, 0, 0, 3,
    -3, 1, 0, 0, -3, -2, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -16, 0, 6, 0, 0, -2, 0,
    0, 0, 0, 1, 0, -3, -3, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 3,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserratMedium_16 = {
#else
lv_font_t lv_font_montserratMedium_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font  default: (f.src.ascent - f.src.descent)*/
    .base_line = 2,                          /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_16*/

