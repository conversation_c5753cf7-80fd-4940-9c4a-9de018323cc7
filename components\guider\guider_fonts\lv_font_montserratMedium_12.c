/*
 * Copyright 2025 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */
/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: --user-data-dir=C:\Users\<USER>\AppData\Roaming\gui-guider --app-path=D:\GUI_Guider\Gui-Guider\resources\app.asar --no-sandbox --no-zygote --lang=zh-CN --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1755494819112641 --launch-time-ticks=12041316375 --mojo-platform-channel-handle=2956 --field-trial-handle=1712,i,16818944610642394718,15139860505302627447,131072 --disable-features=SpareRendererForSitePerProcess,WinRetrieveSuggestionsOnlyOnDemand /prefetch:1
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_12
#define LV_FONT_MONTSERRATMEDIUM_12 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x40, 0xf3, 0xf, 0x30, 0xf2, 0xe, 0x20,
    0xd1, 0x3, 0x0, 0x81, 0x1e, 0x30,

    /* U+0022 "\"" */
    0x3c, 0x1e, 0x3b, 0xe, 0x3b, 0xe, 0x15, 0x7,

    /* U+0023 "#" */
    0x0, 0x48, 0x3, 0xa0, 0x0, 0x6, 0x60, 0x58,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0x10, 0xa, 0x20,
    0x84, 0x0, 0x0, 0xc1, 0xa, 0x30, 0x0, 0xd,
    0x0, 0xb1, 0x0, 0xaf, 0xff, 0xff, 0xfb, 0x0,
    0x1c, 0x0, 0xd0, 0x0, 0x3, 0xa0, 0x1c, 0x0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x9, 0x20, 0x0, 0x0, 0x92, 0x0, 0x3,
    0xcf, 0xfb, 0x31, 0xf7, 0xa5, 0x74, 0x4e, 0x9,
    0x20, 0x1, 0xf9, 0xb2, 0x0, 0x2, 0xbf, 0xe8,
    0x0, 0x0, 0x97, 0xda, 0x0, 0x9, 0x24, 0xe5,
    0xb4, 0xa5, 0xba, 0x8, 0xef, 0xfa, 0x10, 0x0,
    0x92, 0x0, 0x0, 0x4, 0x10, 0x0,

    /* U+0025 "%" */
    0xa, 0xc8, 0x0, 0xc, 0x10, 0x66, 0xa, 0x20,
    0x76, 0x0, 0x83, 0x7, 0x42, 0xc0, 0x0, 0x57,
    0xa, 0x2b, 0x20, 0x0, 0x9, 0xc6, 0x68, 0x5c,
    0x90, 0x0, 0x1, 0xc1, 0xc0, 0x67, 0x0, 0xa,
    0x43, 0x90, 0x2a, 0x0, 0x49, 0x1, 0xb0, 0x47,
    0x0, 0xc1, 0x0, 0x7b, 0xb1,

    /* U+0026 "&" */
    0x0, 0x9e, 0xd4, 0x0, 0x0, 0x5c, 0x3, 0xd0,
    0x0, 0x4, 0xc0, 0x5c, 0x0, 0x0, 0xc, 0xbd,
    0x20, 0x0, 0x3, 0xde, 0x80, 0x10, 0x1, 0xe3,
    0x1d, 0x78, 0x80, 0x6b, 0x0, 0x1d, 0xf2, 0x4,
    0xf4, 0x13, 0xcf, 0x60, 0x6, 0xdf, 0xd6, 0x2b,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x3c, 0x3b, 0x3b, 0x15,

    /* U+0028 "(" */
    0xa, 0x71, 0xf1, 0x5c, 0x9, 0x80, 0xb6, 0xc,
    0x40, 0xd4, 0xc, 0x40, 0xb6, 0x9, 0x80, 0x5b,
    0x1, 0xf1, 0xa, 0x70,

    /* U+0029 ")" */
    0x6b, 0x0, 0xf2, 0xb, 0x60, 0x7a, 0x5, 0xc0,
    0x4d, 0x3, 0xe0, 0x4d, 0x5, 0xc0, 0x7a, 0xb,
    0x60, 0xf1, 0x6b, 0x0,

    /* U+002A "*" */
    0x0, 0xb0, 0x8, 0x9c, 0xb5, 0xb, 0xf8, 0x8,
    0x7c, 0x95, 0x0, 0xa0, 0x0,

    /* U+002B "+" */
    0x0, 0xb, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0xf, 0x0, 0x2, 0xee, 0xfe, 0xe2, 0x1, 0x1f,
    0x11, 0x0, 0x0, 0xf0, 0x0,

    /* U+002D "-" */
    0x4f, 0xfd, 0x2, 0x22,

    /* U+002E "." */
    0x2a, 0x4, 0xd0,

    /* U+002F "/" */
    0x0, 0x0, 0x34, 0x0, 0x0, 0xb5, 0x0, 0x0,
    0xf0, 0x0, 0x5, 0xb0, 0x0, 0xa, 0x60, 0x0,
    0xe, 0x10, 0x0, 0x4c, 0x0, 0x0, 0x97, 0x0,
    0x0, 0xe2, 0x0, 0x3, 0xd0, 0x0, 0x8, 0x70,
    0x0, 0xd, 0x20, 0x0, 0x2d, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x9e, 0xe9, 0x0, 0xa, 0xd4, 0x4d, 0xa0,
    0x1f, 0x20, 0x2, 0xf1, 0x5e, 0x0, 0x0, 0xd5,
    0x6c, 0x0, 0x0, 0xc6, 0x5e, 0x0, 0x0, 0xd5,
    0x1f, 0x20, 0x2, 0xf1, 0xa, 0xd4, 0x4d, 0xa0,
    0x0, 0x9e, 0xe9, 0x0,

    /* U+0031 "1" */
    0xef, 0xf3, 0x22, 0xf3, 0x0, 0xf3, 0x0, 0xf3,
    0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf3, 0x0, 0xf3,
    0x0, 0xf3,

    /* U+0032 "2" */
    0x19, 0xef, 0xc2, 0x8, 0xb4, 0x3a, 0xe0, 0x0,
    0x0, 0x2f, 0x10, 0x0, 0x5, 0xe0, 0x0, 0x2,
    0xe5, 0x0, 0x1, 0xd7, 0x0, 0x1, 0xd8, 0x0,
    0x1, 0xda, 0x22, 0x21, 0x8f, 0xff, 0xff, 0x70,

    /* U+0033 "3" */
    0x9f, 0xff, 0xff, 0x1, 0x22, 0x2d, 0x80, 0x0,
    0x9, 0xb0, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x7c,
    0xf8, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0xe,
    0x4b, 0x94, 0x39, 0xf1, 0x3b, 0xff, 0xc3, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x9b, 0x0, 0x0, 0x4, 0xe1, 0x0,
    0x0, 0x1e, 0x50, 0x0, 0x0, 0xaa, 0x0, 0x0,
    0x5, 0xe1, 0xd, 0x40, 0x1e, 0x40, 0xd, 0x40,
    0x8f, 0xff, 0xff, 0xfd, 0x12, 0x22, 0x2e, 0x62,
    0x0, 0x0, 0xe, 0x40,

    /* U+0035 "5" */
    0xc, 0xff, 0xff, 0x0, 0xe5, 0x22, 0x20, 0xf,
    0x10, 0x0, 0x1, 0xff, 0xeb, 0x30, 0x2, 0x23,
    0x9f, 0x10, 0x0, 0x0, 0xd6, 0x0, 0x0, 0xd,
    0x69, 0xb4, 0x38, 0xf1, 0x2a, 0xef, 0xc4, 0x0,

    /* U+0036 "6" */
    0x0, 0x6d, 0xfd, 0x50, 0x8, 0xd5, 0x23, 0x20,
    0x1f, 0x20, 0x0, 0x0, 0x4d, 0x6d, 0xea, 0x10,
    0x6f, 0xc4, 0x3c, 0xa0, 0x5f, 0x30, 0x2, 0xf0,
    0x2f, 0x20, 0x2, 0xf0, 0xa, 0xc3, 0x2b, 0xa0,
    0x1, 0xaf, 0xfa, 0x10,

    /* U+0037 "7" */
    0xaf, 0xff, 0xff, 0xba, 0x92, 0x22, 0xd7, 0x76,
    0x0, 0x3f, 0x10, 0x0, 0xa, 0x90, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x7c, 0x0, 0x0, 0xe, 0x50,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xc8, 0x0, 0x0,

    /* U+0038 "8" */
    0x3, 0xcf, 0xea, 0x10, 0xe, 0x81, 0x2c, 0xa0,
    0x2f, 0x10, 0x5, 0xd0, 0xe, 0x70, 0x1b, 0x90,
    0x6, 0xff, 0xff, 0x20, 0x3f, 0x50, 0x18, 0xe0,
    0x6c, 0x0, 0x0, 0xf2, 0x3f, 0x61, 0x29, 0xe0,
    0x5, 0xcf, 0xfb, 0x20,

    /* U+0039 "9" */
    0x7, 0xef, 0xc3, 0x6, 0xe3, 0x15, 0xe1, 0x98,
    0x0, 0xb, 0x87, 0xd2, 0x3, 0xfb, 0xa, 0xff,
    0xd9, 0xc0, 0x0, 0x10, 0x8b, 0x0, 0x0, 0xd,
    0x70, 0x62, 0x4b, 0xd0, 0x1c, 0xfe, 0xa1, 0x0,

    /* U+003A ":" */
    0x4e, 0x2, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xa0, 0x4d, 0x0,

    /* U+003B ";" */
    0x4e, 0x2, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x80, 0x4f, 0x10, 0xd0, 0x38, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x2, 0x10, 0x0, 0x4b, 0xe1, 0x7,
    0xdc, 0x50, 0x3, 0xf8, 0x0, 0x0, 0x4, 0xbe,
    0x71, 0x0, 0x0, 0x29, 0xe2, 0x0, 0x0, 0x0,
    0x0,

    /* U+003D "=" */
    0x3f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0xee, 0xe2, 0x1, 0x11,
    0x11, 0x0,

    /* U+003E ">" */
    0x12, 0x0, 0x0, 0x2, 0xeb, 0x40, 0x0, 0x0,
    0x5c, 0xd6, 0x0, 0x0, 0x8, 0xf2, 0x1, 0x7e,
    0xb4, 0x2, 0xe9, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x1a, 0xef, 0xc3, 0x9, 0xa3, 0x2a, 0xe0, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x9,
    0xc0, 0x0, 0x2, 0xf1, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x2, 0x80, 0x0, 0x0, 0x4d, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x4, 0xbd, 0xdd, 0x81, 0x0, 0x0, 0x9b,
    0x30, 0x0, 0x6d, 0x30, 0x7, 0xa0, 0x8e, 0xe8,
    0xd5, 0xd1, 0xd, 0x7, 0xd2, 0x19, 0xf3, 0x77,
    0x4a, 0xd, 0x40, 0x0, 0xf3, 0x1b, 0x58, 0xf,
    0x20, 0x0, 0xd3, 0xc, 0x58, 0xd, 0x40, 0x0,
    0xf3, 0x1b, 0x3a, 0x7, 0xd2, 0x1a, 0xf5, 0x77,
    0xd, 0x0, 0x8e, 0xe8, 0x5f, 0xb0, 0x6, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9b, 0x30, 0x2,
    0x40, 0x0, 0x0, 0x5, 0xbd, 0xed, 0x60, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x0, 0xdd,
    0x90, 0x0, 0x0, 0x4, 0xe3, 0xf1, 0x0, 0x0,
    0xb, 0x80, 0xc7, 0x0, 0x0, 0x1f, 0x20, 0x6e,
    0x0, 0x0, 0x8c, 0x0, 0x1f, 0x50, 0x0, 0xef,
    0xee, 0xef, 0xb0, 0x6, 0xe2, 0x11, 0x14, 0xf2,
    0xc, 0x70, 0x0, 0x0, 0xb9,

    /* U+0042 "B" */
    0xbf, 0xff, 0xfb, 0x20, 0xb7, 0x11, 0x2a, 0xd0,
    0xb7, 0x0, 0x3, 0xf0, 0xb7, 0x0, 0x8, 0xc0,
    0xbf, 0xff, 0xff, 0x50, 0xb8, 0x22, 0x26, 0xf2,
    0xb7, 0x0, 0x0, 0xc7, 0xb7, 0x11, 0x15, 0xf4,
    0xbf, 0xff, 0xfd, 0x60,

    /* U+0043 "C" */
    0x0, 0x3b, 0xef, 0xb3, 0x0, 0x5f, 0x93, 0x38,
    0xe0, 0xe, 0x60, 0x0, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x0, 0x6c, 0x0, 0x0, 0x0, 0x4, 0xe0,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x0,
    0x5f, 0x93, 0x38, 0xe0, 0x0, 0x3b, 0xff, 0xb3,
    0x0,

    /* U+0044 "D" */
    0xbf, 0xff, 0xea, 0x30, 0xb, 0x82, 0x23, 0x9f,
    0x40, 0xb7, 0x0, 0x0, 0x7e, 0xb, 0x70, 0x0,
    0x0, 0xf3, 0xb7, 0x0, 0x0, 0xe, 0x5b, 0x70,
    0x0, 0x0, 0xf3, 0xb7, 0x0, 0x0, 0x7e, 0xb,
    0x82, 0x23, 0x9f, 0x40, 0xbf, 0xff, 0xeb, 0x30,
    0x0,

    /* U+0045 "E" */
    0xbf, 0xff, 0xff, 0x3b, 0x82, 0x22, 0x20, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xbf, 0xff,
    0xfa, 0xb, 0x82, 0x22, 0x10, 0xb7, 0x0, 0x0,
    0xb, 0x82, 0x22, 0x20, 0xbf, 0xff, 0xff, 0x50,

    /* U+0046 "F" */
    0xbf, 0xff, 0xff, 0x3b, 0x82, 0x22, 0x20, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xbf, 0xff,
    0xfa, 0xb, 0x82, 0x22, 0x10, 0xb7, 0x0, 0x0,
    0xb, 0x70, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x3b, 0xef, 0xc4, 0x0, 0x5f, 0x94, 0x38,
    0xe1, 0xe, 0x70, 0x0, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0x0, 0x6c, 0x0, 0x0, 0x8, 0x24, 0xe0,
    0x0, 0x0, 0xe3, 0xe, 0x60, 0x0, 0xe, 0x30,
    0x5f, 0x93, 0x37, 0xf3, 0x0, 0x3b, 0xef, 0xc4,
    0x0,

    /* U+0048 "H" */
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xbf, 0xff, 0xff, 0xf7, 0xb8, 0x22, 0x22, 0xc7,
    0xb7, 0x0, 0x0, 0xb7, 0xb7, 0x0, 0x0, 0xb7,
    0xb7, 0x0, 0x0, 0xb7,

    /* U+0049 "I" */
    0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7,
    0xb7,

    /* U+004A "J" */
    0x4, 0xff, 0xff, 0x0, 0x22, 0x5f, 0x0, 0x0,
    0x3f, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x3f, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x4e, 0xd, 0x52, 0xba,
    0x5, 0xdf, 0xb2,

    /* U+004B "K" */
    0xb7, 0x0, 0x7, 0xd1, 0xb7, 0x0, 0x5e, 0x20,
    0xb7, 0x4, 0xe3, 0x0, 0xb7, 0x3e, 0x40, 0x0,
    0xb9, 0xef, 0x20, 0x0, 0xbf, 0x89, 0xd0, 0x0,
    0xba, 0x0, 0xca, 0x0, 0xb7, 0x0, 0x1e, 0x70,
    0xb7, 0x0, 0x3, 0xf3,

    /* U+004C "L" */
    0xb7, 0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x0, 0xb7, 0x0, 0x0,
    0xb, 0x82, 0x22, 0x20, 0xbf, 0xff, 0xff, 0x0,

    /* U+004D "M" */
    0xb8, 0x0, 0x0, 0x1, 0xf3, 0xbf, 0x10, 0x0,
    0x9, 0xf3, 0xbe, 0xa0, 0x0, 0x2e, 0xf3, 0xb7,
    0xe3, 0x0, 0xb7, 0xf3, 0xb6, 0x7b, 0x4, 0xd0,
    0xf3, 0xb6, 0xd, 0x4c, 0x50, 0xf3, 0xb6, 0x5,
    0xfc, 0x0, 0xf3, 0xb6, 0x0, 0xb3, 0x0, 0xf3,
    0xb6, 0x0, 0x0, 0x0, 0xf3,

    /* U+004E "N" */
    0xb9, 0x0, 0x0, 0xb7, 0xbf, 0x50, 0x0, 0xb7,
    0xbc, 0xf2, 0x0, 0xb7, 0xb7, 0xad, 0x0, 0xb7,
    0xb7, 0xd, 0x90, 0xb7, 0xb7, 0x2, 0xf5, 0xb7,
    0xb7, 0x0, 0x6f, 0xd7, 0xb7, 0x0, 0xa, 0xf7,
    0xb7, 0x0, 0x0, 0xd7,

    /* U+004F "O" */
    0x0, 0x3b, 0xef, 0xb4, 0x0, 0x5, 0xf9, 0x33,
    0x8f, 0x60, 0xe, 0x60, 0x0, 0x5, 0xf1, 0x4e,
    0x0, 0x0, 0x0, 0xd5, 0x6c, 0x0, 0x0, 0x0,
    0xb7, 0x4e, 0x0, 0x0, 0x0, 0xd5, 0xe, 0x60,
    0x0, 0x5, 0xf1, 0x5, 0xf9, 0x33, 0x8f, 0x60,
    0x0, 0x3b, 0xef, 0xb4, 0x0,

    /* U+0050 "P" */
    0xbf, 0xff, 0xd8, 0x0, 0xb8, 0x22, 0x5d, 0x90,
    0xb7, 0x0, 0x4, 0xe0, 0xb7, 0x0, 0x3, 0xf0,
    0xb7, 0x0, 0x2c, 0xa0, 0xbf, 0xff, 0xfa, 0x10,
    0xb8, 0x22, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,
    0xb7, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x3b, 0xef, 0xb4, 0x0, 0x4, 0xf9, 0x33,
    0x8f, 0x60, 0xe, 0x60, 0x0, 0x5, 0xf1, 0x4e,
    0x0, 0x0, 0x0, 0xd5, 0x6c, 0x0, 0x0, 0x0,
    0xb7, 0x4e, 0x0, 0x0, 0x0, 0xd6, 0xf, 0x60,
    0x0, 0x5, 0xf1, 0x5, 0xf8, 0x32, 0x7f, 0x60,
    0x0, 0x4c, 0xff, 0xc5, 0x0, 0x0, 0x0, 0xc,
    0xb0, 0x28, 0x0, 0x0, 0x1, 0xbf, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0052 "R" */
    0xbf, 0xff, 0xd8, 0x0, 0xb8, 0x22, 0x5d, 0x90,
    0xb7, 0x0, 0x4, 0xe0, 0xb7, 0x0, 0x3, 0xf0,
    0xb7, 0x0, 0x1b, 0xb0, 0xbf, 0xff, 0xfb, 0x10,
    0xb8, 0x22, 0xb9, 0x0, 0xb7, 0x0, 0x1f, 0x30,
    0xb7, 0x0, 0x7, 0xd0,

    /* U+0053 "S" */
    0x3, 0xcf, 0xeb, 0x31, 0xf7, 0x23, 0x74, 0x4e,
    0x0, 0x0, 0x1, 0xf9, 0x20, 0x0, 0x2, 0xbf,
    0xd7, 0x0, 0x0, 0x4, 0xca, 0x0, 0x0, 0x4,
    0xe5, 0xb4, 0x23, 0xbb, 0x8, 0xdf, 0xea, 0x10,

    /* U+0054 "T" */
    0xff, 0xff, 0xff, 0xf2, 0x23, 0xf3, 0x22, 0x0,
    0x1f, 0x10, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f,
    0x10, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f, 0x10,
    0x0, 0x1, 0xf1, 0x0, 0x0, 0x1f, 0x10, 0x0,

    /* U+0055 "U" */
    0xd6, 0x0, 0x0, 0xe4, 0xd6, 0x0, 0x0, 0xe4,
    0xd6, 0x0, 0x0, 0xe4, 0xd6, 0x0, 0x0, 0xe4,
    0xd6, 0x0, 0x0, 0xe4, 0xc7, 0x0, 0x0, 0xf3,
    0x9a, 0x0, 0x2, 0xf1, 0x2f, 0x83, 0x5d, 0xa0,
    0x4, 0xcf, 0xd8, 0x0,

    /* U+0056 "V" */
    0xc, 0x70, 0x0, 0x0, 0xd5, 0x6, 0xe0, 0x0,
    0x4, 0xe0, 0x0, 0xf4, 0x0, 0xa, 0x80, 0x0,
    0x9b, 0x0, 0x1f, 0x20, 0x0, 0x2f, 0x20, 0x7b,
    0x0, 0x0, 0xc, 0x80, 0xe4, 0x0, 0x0, 0x5,
    0xe5, 0xe0, 0x0, 0x0, 0x0, 0xee, 0x70, 0x0,
    0x0, 0x0, 0x8f, 0x10, 0x0,

    /* U+0057 "W" */
    0x7c, 0x0, 0x0, 0xe8, 0x0, 0x2, 0xf0, 0x2f,
    0x10, 0x3, 0xfd, 0x0, 0x7, 0xa0, 0xd, 0x60,
    0x8, 0x9f, 0x20, 0xc, 0x50, 0x8, 0xb0, 0xe,
    0x3b, 0x70, 0x1f, 0x0, 0x3, 0xf0, 0x3e, 0x6,
    0xc0, 0x6b, 0x0, 0x0, 0xe5, 0x89, 0x1, 0xf1,
    0xb6, 0x0, 0x0, 0x9a, 0xd4, 0x0, 0xb7, 0xf1,
    0x0, 0x0, 0x4f, 0xe0, 0x0, 0x6f, 0xc0, 0x0,
    0x0, 0xf, 0xa0, 0x0, 0x1f, 0x70, 0x0,

    /* U+0058 "X" */
    0x5f, 0x10, 0x0, 0xe5, 0xa, 0xb0, 0x9, 0xa0,
    0x1, 0xe6, 0x4e, 0x10, 0x0, 0x4f, 0xe4, 0x0,
    0x0, 0xd, 0xe0, 0x0, 0x0, 0x7d, 0xd8, 0x0,
    0x2, 0xf3, 0x2f, 0x30, 0xc, 0x80, 0x7, 0xd0,
    0x8d, 0x0, 0x0, 0xc9,

    /* U+0059 "Y" */
    0xc, 0x80, 0x0, 0xa, 0x80, 0x3f, 0x10, 0x3,
    0xe0, 0x0, 0xaa, 0x0, 0xc6, 0x0, 0x1, 0xf3,
    0x5d, 0x0, 0x0, 0x7, 0xce, 0x40, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x0, 0x0, 0xb7, 0x0,
    0x0,

    /* U+005A "Z" */
    0x6f, 0xff, 0xff, 0xf5, 0x2, 0x22, 0x29, 0xd0,
    0x0, 0x0, 0x3f, 0x30, 0x0, 0x1, 0xe6, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x4, 0xf2, 0x0, 0x0, 0x1e, 0x82, 0x22, 0x21,
    0x7f, 0xff, 0xff, 0xf8,

    /* U+005B "[" */
    0xbf, 0xcb, 0x60, 0xb6, 0xb, 0x60, 0xb6, 0xb,
    0x60, 0xb6, 0xb, 0x60, 0xb6, 0xb, 0x60, 0xb6,
    0xb, 0x60, 0xbf, 0xc0,

    /* U+005C "\\" */
    0x35, 0x0, 0x0, 0x2e, 0x0, 0x0, 0xd, 0x30,
    0x0, 0x8, 0x80, 0x0, 0x3, 0xd0, 0x0, 0x0,
    0xd2, 0x0, 0x0, 0x87, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0xe, 0x10, 0x0, 0x9, 0x70, 0x0, 0x4,
    0xc0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0xa6,

    /* U+005D "]" */
    0xcf, 0xb0, 0x7b, 0x6, 0xb0, 0x6b, 0x6, 0xb0,
    0x6b, 0x6, 0xb0, 0x6b, 0x6, 0xb0, 0x6b, 0x6,
    0xb0, 0x7b, 0xcf, 0xb0,

    /* U+005E "^" */
    0x0, 0x7, 0x0, 0x0, 0x5, 0xe5, 0x0, 0x0,
    0xb4, 0xb0, 0x0, 0x2c, 0xc, 0x20, 0x8, 0x60,
    0x68, 0x0, 0xd0, 0x0, 0xd0,

    /* U+005F "_" */
    0xdd, 0xdd, 0xdd,

    /* U+0060 "`" */
    0x27, 0x10, 0x5, 0xc1,

    /* U+0061 "a" */
    0x8, 0xdf, 0xc3, 0x0, 0xa4, 0x29, 0xd0, 0x0,
    0x0, 0x1f, 0x10, 0x8d, 0xee, 0xf2, 0x4e, 0x10,
    0xf, 0x24, 0xe0, 0x7, 0xf2, 0x9, 0xed, 0x8f,
    0x20,

    /* U+0062 "b" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x7c, 0xfc, 0x40, 0xef, 0x52,
    0x8f, 0x2e, 0x60, 0x0, 0xb8, 0xe4, 0x0, 0x8,
    0xae, 0x60, 0x0, 0xb8, 0xef, 0x52, 0x8f, 0x2e,
    0x6d, 0xfc, 0x40,

    /* U+0063 "c" */
    0x2, 0xbf, 0xe8, 0x0, 0xda, 0x24, 0xc3, 0x5d,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x5d, 0x0,
    0x0, 0x0, 0xda, 0x24, 0xd3, 0x2, 0xbf, 0xe8,
    0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x1, 0xf1, 0x2, 0xbf, 0xd6, 0xf1,
    0xe, 0x92, 0x3d, 0xf1, 0x5d, 0x0, 0x4, 0xf1,
    0x7b, 0x0, 0x1, 0xf1, 0x5d, 0x0, 0x3, 0xf1,
    0xe, 0x91, 0x2d, 0xf1, 0x2, 0xbf, 0xe6, 0xf1,

    /* U+0065 "e" */
    0x2, 0xbf, 0xd5, 0x0, 0xe8, 0x14, 0xe4, 0x5c,
    0x0, 0x6, 0xb7, 0xfe, 0xee, 0xec, 0x5d, 0x0,
    0x0, 0x0, 0xe9, 0x23, 0xa2, 0x2, 0xbf, 0xe9,
    0x0,

    /* U+0066 "f" */
    0x1, 0xcf, 0x60, 0x9a, 0x11, 0xb, 0x60, 0xd,
    0xff, 0xf3, 0xb, 0x60, 0x0, 0xb6, 0x0, 0xb,
    0x60, 0x0, 0xb6, 0x0, 0xb, 0x60, 0x0, 0xb6,
    0x0,

    /* U+0067 "g" */
    0x2, 0xbf, 0xe6, 0xe2, 0xe, 0xa2, 0x3c, 0xf2,
    0x5d, 0x0, 0x2, 0xf2, 0x7b, 0x0, 0x0, 0xf2,
    0x5d, 0x0, 0x2, 0xf2, 0xe, 0xa2, 0x3d, 0xf2,
    0x2, 0xbf, 0xe5, 0xf2, 0x0, 0x0, 0x2, 0xf0,
    0xc, 0x62, 0x3b, 0xa0, 0x6, 0xdf, 0xea, 0x10,

    /* U+0068 "h" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x7d, 0xfc, 0x20, 0xee, 0x42,
    0xac, 0xe, 0x60, 0x2, 0xf0, 0xe4, 0x0, 0xf,
    0x1e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf, 0x2e,
    0x40, 0x0, 0xf2,

    /* U+0069 "i" */
    0xd, 0x40, 0x82, 0x0, 0x0, 0xe4, 0xe, 0x40,
    0xe4, 0xe, 0x40, 0xe4, 0xe, 0x40, 0xe4,

    /* U+006A "j" */
    0x0, 0xd, 0x50, 0x0, 0x72, 0x0, 0x0, 0x0,
    0x0, 0xd4, 0x0, 0xd, 0x40, 0x0, 0xd4, 0x0,
    0xd, 0x40, 0x0, 0xd4, 0x0, 0xd, 0x40, 0x0,
    0xd4, 0x0, 0xd, 0x40, 0x22, 0xf2, 0xd, 0xf8,
    0x0,

    /* U+006B "k" */
    0xe4, 0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0xe, 0x40, 0xb, 0xa0, 0xe4, 0xb,
    0xb0, 0xe, 0x4b, 0xc0, 0x0, 0xee, 0xfd, 0x0,
    0xe, 0xc1, 0xd9, 0x0, 0xe4, 0x2, 0xf4, 0xe,
    0x40, 0x6, 0xe1,

    /* U+006C "l" */
    0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4, 0xe4,
    0xe4, 0xe4,

    /* U+006D "m" */
    0xe7, 0xdf, 0xb2, 0x9e, 0xe8, 0xe, 0xd3, 0x2c,
    0xfb, 0x23, 0xe5, 0xe6, 0x0, 0x4f, 0x10, 0x9,
    0x9e, 0x40, 0x3, 0xf0, 0x0, 0x8a, 0xe4, 0x0,
    0x3f, 0x0, 0x8, 0xae, 0x40, 0x3, 0xf0, 0x0,
    0x8a, 0xe4, 0x0, 0x3f, 0x0, 0x8, 0xa0,

    /* U+006E "n" */
    0xe6, 0xdf, 0xc2, 0xe, 0xe4, 0x1a, 0xc0, 0xe6,
    0x0, 0x1f, 0xe, 0x40, 0x0, 0xf1, 0xe4, 0x0,
    0xf, 0x2e, 0x40, 0x0, 0xf2, 0xe4, 0x0, 0xf,
    0x20,

    /* U+006F "o" */
    0x2, 0xbf, 0xe8, 0x0, 0xe, 0xa2, 0x3e, 0x80,
    0x5d, 0x0, 0x4, 0xf0, 0x7b, 0x0, 0x1, 0xf1,
    0x5d, 0x0, 0x4, 0xf0, 0xd, 0xa2, 0x3e, 0x80,
    0x2, 0xbf, 0xe8, 0x0,

    /* U+0070 "p" */
    0xe7, 0xdf, 0xc4, 0xe, 0xf4, 0x16, 0xf2, 0xe6,
    0x0, 0xa, 0x8e, 0x40, 0x0, 0x8a, 0xe7, 0x0,
    0xb, 0x8e, 0xf5, 0x28, 0xf2, 0xe6, 0xcf, 0xc4,
    0xe, 0x40, 0x0, 0x0, 0xe4, 0x0, 0x0, 0xe,
    0x40, 0x0, 0x0,

    /* U+0071 "q" */
    0x2, 0xbf, 0xd5, 0xf1, 0xe, 0xa2, 0x3e, 0xf1,
    0x5d, 0x0, 0x4, 0xf1, 0x7b, 0x0, 0x1, 0xf1,
    0x5d, 0x0, 0x4, 0xf1, 0xe, 0xa2, 0x3e, 0xf1,
    0x2, 0xbf, 0xd5, 0xf1, 0x0, 0x0, 0x1, 0xf1,
    0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x1, 0xf1,

    /* U+0072 "r" */
    0xe6, 0xd8, 0xee, 0x61, 0xe7, 0x0, 0xe4, 0x0,
    0xe4, 0x0, 0xe4, 0x0, 0xe4, 0x0,

    /* U+0073 "s" */
    0x9, 0xef, 0xc2, 0x6d, 0x22, 0x61, 0x7d, 0x20,
    0x0, 0x9, 0xfe, 0x91, 0x0, 0x2, 0xc9, 0x56,
    0x22, 0xb8, 0x4c, 0xfe, 0xa0,

    /* U+0074 "t" */
    0x5, 0x30, 0x0, 0xb6, 0x0, 0xdf, 0xff, 0x30,
    0xb6, 0x0, 0xb, 0x60, 0x0, 0xb6, 0x0, 0xb,
    0x60, 0x0, 0xaa, 0x11, 0x2, 0xdf, 0x60,

    /* U+0075 "u" */
    0xf3, 0x0, 0x2f, 0xf, 0x30, 0x2, 0xf0, 0xf3,
    0x0, 0x2f, 0xf, 0x30, 0x2, 0xf0, 0xe4, 0x0,
    0x4f, 0xa, 0xb2, 0x2c, 0xf0, 0x1b, 0xfe, 0x6f,
    0x0,

    /* U+0076 "v" */
    0xd, 0x50, 0x0, 0x98, 0x6, 0xc0, 0x0, 0xf2,
    0x1, 0xf2, 0x6, 0xb0, 0x0, 0xa8, 0xc, 0x50,
    0x0, 0x3e, 0x3e, 0x0, 0x0, 0xd, 0xd8, 0x0,
    0x0, 0x6, 0xf2, 0x0,

    /* U+0077 "w" */
    0xc5, 0x0, 0x3f, 0x10, 0x7, 0x86, 0xa0, 0x9,
    0xf6, 0x0, 0xd3, 0x1f, 0x0, 0xe7, 0xb0, 0x2d,
    0x0, 0xb5, 0x4c, 0xe, 0x18, 0x80, 0x6, 0xa9,
    0x60, 0xa6, 0xd3, 0x0, 0x1f, 0xe1, 0x4, 0xed,
    0x0, 0x0, 0xbb, 0x0, 0xe, 0x80, 0x0,

    /* U+0078 "x" */
    0x5d, 0x0, 0x4e, 0x10, 0xa9, 0x1e, 0x40, 0x1,
    0xed, 0x90, 0x0, 0x8, 0xf1, 0x0, 0x2, 0xeb,
    0xa0, 0x0, 0xc7, 0xd, 0x60, 0x7c, 0x0, 0x3f,
    0x20,

    /* U+0079 "y" */
    0xd, 0x50, 0x0, 0x98, 0x7, 0xb0, 0x0, 0xe2,
    0x1, 0xf2, 0x5, 0xc0, 0x0, 0xa7, 0xb, 0x50,
    0x0, 0x4d, 0x1e, 0x0, 0x0, 0xe, 0xb9, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x5, 0xd0, 0x0,
    0x5, 0x2c, 0x60, 0x0, 0x1c, 0xf9, 0x0, 0x0,

    /* U+007A "z" */
    0x7f, 0xff, 0xfb, 0x0, 0x2, 0xf3, 0x0, 0xc,
    0x70, 0x0, 0x9b, 0x0, 0x4, 0xe1, 0x0, 0x1e,
    0x50, 0x0, 0x8f, 0xff, 0xfd,

    /* U+007B "{" */
    0x0, 0xbf, 0x4, 0xe1, 0x5, 0xc0, 0x5, 0xc0,
    0x5, 0xc0, 0x6, 0xc0, 0x4f, 0x60, 0x8, 0xc0,
    0x5, 0xc0, 0x5, 0xc0, 0x5, 0xc0, 0x4, 0xe1,
    0x0, 0xbf,

    /* U+007C "|" */
    0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5, 0xb5,
    0xb5, 0xb5, 0xb5, 0xb5, 0xb5,

    /* U+007D "}" */
    0xcd, 0x10, 0xc, 0x70, 0x9, 0x90, 0x9, 0x90,
    0x9, 0x90, 0x8, 0xa0, 0x3, 0xf7, 0x8, 0xb0,
    0x9, 0x90, 0x9, 0x90, 0x9, 0x90, 0xc, 0x80,
    0xcd, 0x20,

    /* U+007E "~" */
    0xb, 0xe8, 0xa, 0x33, 0x91, 0x8d, 0xa0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F043 "" */
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x10,
    0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0xa, 0xff,
    0xe0, 0x0, 0x4, 0xff, 0xff, 0x80, 0x0, 0xef,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xfc, 0xe,
    0xff, 0xff, 0xff, 0xf2, 0xf9, 0xcf, 0xff, 0xff,
    0x3d, 0xc5, 0xff, 0xff, 0xf1, 0x6f, 0xa3, 0xbf,
    0xfa, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x0, 0x26,
    0x74, 0x0, 0x0,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xc, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x27, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfe,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x12, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9a, 0xaa, 0xaa, 0xaa,
    0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0xc3, 0xbf, 0xff, 0xff, 0xfb, 0x3c,
    0xff, 0x57, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf9,
    0x3d, 0xd3, 0x9f, 0xff, 0xff, 0xff, 0xd5, 0x5d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x2, 0xef, 0xfa, 0x5d,
    0x20, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x2, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x2, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 52, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 51, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14, .adv_w = 75, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 22, .adv_w = 135, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 63, .adv_w = 119, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 109, .adv_w = 162, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 154, .adv_w = 132, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 199, .adv_w = 40, .box_w = 2, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 203, .adv_w = 65, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 223, .adv_w = 65, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 243, .adv_w = 77, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 256, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 277, .adv_w = 74, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 281, .adv_w = 44, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 284, .adv_w = 68, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 323, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 359, .adv_w = 71, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 377, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 409, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 441, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 477, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 509, .adv_w = 118, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 545, .adv_w = 115, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 577, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 613, .adv_w = 118, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 645, .adv_w = 44, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 656, .adv_w = 44, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 670, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 695, .adv_w = 112, .box_w = 7, .box_h = 5, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 713, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 738, .adv_w = 110, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 770, .adv_w = 199, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 842, .adv_w = 141, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 887, .adv_w = 145, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 923, .adv_w = 139, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 964, .adv_w = 159, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1005, .adv_w = 129, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1037, .adv_w = 122, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1069, .adv_w = 148, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1110, .adv_w = 156, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1146, .adv_w = 60, .box_w = 2, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1155, .adv_w = 98, .box_w = 6, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1182, .adv_w = 138, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1218, .adv_w = 114, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1250, .adv_w = 183, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1295, .adv_w = 156, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1331, .adv_w = 161, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1376, .adv_w = 139, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1412, .adv_w = 161, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1472, .adv_w = 140, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1508, .adv_w = 119, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1540, .adv_w = 113, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1572, .adv_w = 152, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1608, .adv_w = 137, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1653, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1716, .adv_w = 129, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1752, .adv_w = 124, .box_w = 9, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1793, .adv_w = 126, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1829, .adv_w = 64, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1849, .adv_w = 68, .box_w = 6, .box_h = 13, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 1888, .adv_w = 64, .box_w = 3, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1908, .adv_w = 112, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1929, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1932, .adv_w = 115, .box_w = 4, .box_h = 2, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1936, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1961, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1996, .adv_w = 110, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2021, .adv_w = 131, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2061, .adv_w = 118, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2086, .adv_w = 68, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2111, .adv_w = 132, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2151, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2186, .adv_w = 54, .box_w = 3, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2201, .adv_w = 55, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2234, .adv_w = 118, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2269, .adv_w = 54, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2279, .adv_w = 203, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2318, .adv_w = 131, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2343, .adv_w = 122, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2371, .adv_w = 131, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2406, .adv_w = 131, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2446, .adv_w = 79, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2460, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2481, .adv_w = 79, .box_w = 5, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2504, .adv_w = 130, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2529, .adv_w = 107, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2557, .adv_w = 173, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2596, .adv_w = 106, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2621, .adv_w = 107, .box_w = 8, .box_h = 10, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2661, .adv_w = 100, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2682, .adv_w = 67, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2708, .adv_w = 57, .box_w = 2, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2721, .adv_w = 67, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2747, .adv_w = 112, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2754, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2832, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2886, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2952, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3006, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3047, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3125, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3203, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3280, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3358, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3421, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3499, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3529, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3574, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3665, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3719, .adv_w = 132, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3778, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3826, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3898, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3959, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4020, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4068, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4134, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4173, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4212, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4273, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4290, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4353, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4457, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4555, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4621, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4660, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 4699, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4779, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4833, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4911, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4996, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5057, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5129, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5190, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5251, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5305, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5364, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5436, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5508, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5571, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5662, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5721, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5811, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5879, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5947, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6015, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6083, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6151, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6239, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6304, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6376, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6461, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6529, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6588, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 12, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 45, .range_length = 82, .glyph_id_start = 13,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 95,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 0, 13, 14, 15, 16, 17, 18,
    19, 12, 20, 20, 0, 0, 0, 21,
    22, 23, 24, 25, 22, 26, 27, 28,
    29, 29, 30, 31, 32, 29, 29, 22,
    33, 34, 35, 3, 36, 30, 37, 37,
    38, 39, 40, 41, 42, 43, 0, 44,
    0, 45, 46, 47, 48, 49, 50, 51,
    45, 52, 52, 53, 48, 45, 45, 46,
    46, 54, 55, 56, 57, 51, 58, 58,
    59, 58, 60, 41, 0, 0, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 9, 10, 11,
    12, 13, 14, 15, 16, 17, 12, 18,
    19, 20, 21, 21, 0, 0, 0, 22,
    23, 24, 25, 23, 25, 25, 25, 23,
    25, 25, 26, 25, 25, 25, 25, 23,
    25, 23, 25, 3, 27, 28, 29, 29,
    30, 31, 32, 33, 34, 35, 0, 36,
    0, 37, 38, 39, 39, 39, 0, 39,
    38, 40, 41, 38, 38, 42, 42, 39,
    42, 39, 42, 43, 44, 45, 46, 46,
    47, 46, 48, 0, 0, 35, 9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 9, 0, 5, -4, 0, 0, 0,
    0, -11, -12, 1, 9, 4, 3, -8,
    1, 9, 1, 8, 2, 6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 2, -1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 0, -4,
    3, 4, 0, 0, -2, 0, -1, 2,
    0, -2, 0, -2, -1, -4, 0, 0,
    0, 0, -2, 0, 0, -2, -3, 0,
    0, -2, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -5, 0, -23, 0, 0, -4, 0,
    4, 6, 0, 0, -4, 2, 2, 6,
    4, -3, 4, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, -9, 0, -8, -1, 0, 0, 0,
    0, 0, 7, 0, -6, -2, -1, 1,
    0, -3, 0, 0, -1, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, -2, 7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 2, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 4, 2, 6, -2, 0, 0, 4,
    -2, -6, -26, 1, 5, 4, 0, -2,
    0, 7, 0, 6, 0, 6, 0, -18,
    0, -2, 6, 0, 6, -2, 4, 2,
    0, 0, 1, -2, 0, 0, -3, 15,
    0, 15, 0, 6, 0, 8, 2, 3,
    0, 0, 0, -7, 0, 0, 0, 0,
    1, -1, 0, 1, -3, -2, -4, 1,
    0, -2, 0, 0, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -11, 0, -12, 0, 0, 0, 0,
    -1, 0, 19, -2, -2, 2, 2, -2,
    0, -2, 2, 0, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 0, -7, 0, 6, 0,
    -13, -19, -13, -4, 6, 0, 0, -13,
    0, 2, -4, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 6, -23, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 1,
    1, -2, -4, 0, -1, -1, -2, 0,
    0, -1, 0, 0, 0, -4, 0, -2,
    0, -4, -4, 0, -5, -6, -6, -4,
    0, -4, 0, -4, 0, 0, 0, 0,
    -2, 0, 0, 2, 0, 1, -2, 0,
    0, 0, 0, 2, -1, 0, 0, 0,
    -1, 2, 2, -1, 0, 0, 0, -4,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 2, -1, 0, -2, 0, -3, 0,
    0, -1, 0, 6, 0, 0, -2, 0,
    0, 0, 0, 0, -1, 1, -1, -1,
    0, -2, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, -1, 0,
    -2, -2, 0, 0, 0, 0, 0, 1,
    0, 0, -1, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, -1, -2, 0,
    0, -6, -1, -6, 4, 0, 0, -4,
    2, 4, 5, 0, -5, -1, -2, 0,
    -1, -9, 2, -1, 1, -10, 2, 0,
    0, 1, -10, 0, -10, -2, -17, -1,
    0, -10, 0, 4, 5, 0, 2, 0,
    0, 0, 0, 0, 0, -3, -2, 0,
    0, 0, 0, -2, 0, 0, 0, -2,
    0, 0, 0, 0, 0, -1, -1, 0,
    -1, -2, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, -1, -2, -2, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -1, 0, -4, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -2, -3, 0,
    0, 6, -1, 1, -6, 0, 0, 5,
    -10, -10, -8, -4, 2, 0, -2, -12,
    -3, 0, -3, 0, -4, 3, -3, -12,
    0, -5, 0, 0, 1, -1, 2, -1,
    0, 2, 0, -6, -7, 0, -10, -5,
    -4, -5, -6, -2, -5, 0, -4, -5,
    0, 1, 0, -2, 0, 0, 0, 1,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, -1,
    0, -1, -2, 0, -3, -4, -4, -1,
    0, -6, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 1, -1, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 9, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, -4, 0, 0, 0,
    0, -10, -6, 0, 0, 0, -3, -10,
    0, 0, -2, 2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 2, 0,
    1, -4, -4, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -6, 0, -2,
    0, -3, -2, 0, -4, -5, -6, -2,
    0, -4, 0, -6, 0, 0, 0, 0,
    15, 0, 0, 1, 0, 0, -2, 0,
    0, -8, 0, 0, 0, 0, 0, -18,
    -3, 6, 6, -2, -8, 0, 2, -3,
    0, -10, -1, -2, 2, -13, -2, 2,
    0, 3, -7, -3, -7, -6, -8, 0,
    0, -12, 0, 11, 0, 0, -1, 0,
    0, 0, -1, -1, -2, -5, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -1, -2, -3, 0,
    0, -4, 0, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 4,
    -1, 2, 0, -4, 2, -1, -1, -5,
    -2, 0, -2, -2, -1, 0, -3, -3,
    0, 0, -2, -1, -1, -3, -2, 0,
    0, -2, 0, 2, -1, 0, -4, 0,
    0, 0, -4, 0, -3, 0, -3, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 2, 0, -3, 0, -1, -2, -6,
    -1, -1, -1, -1, -1, -2, -1, 0,
    0, 0, 0, 0, -2, -2, -2, 0,
    0, 0, 0, 2, -1, 0, -1, 0,
    0, 0, -1, -2, -1, -2, -2, -2,
    2, 8, -1, 0, -5, 0, -1, 4,
    0, -2, -8, -2, 3, 0, 0, -9,
    -3, 2, -3, 1, 0, -1, -2, -6,
    0, -3, 1, 0, 0, -3, 0, 0,
    0, 2, 2, -4, -4, 0, -3, -2,
    -3, -2, -2, 0, -3, 1, -4, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, -2, 0, 0, -2, -2, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -1, 0,
    0, 0, -3, 0, -4, 0, 0, 0,
    -6, 0, 1, -4, 4, 0, -1, -9,
    0, 0, -4, -2, 0, -8, -5, -5,
    0, 0, -8, -2, -8, -7, -9, 0,
    -5, 0, 2, 13, -2, 0, -4, -2,
    -1, -2, -3, -5, -3, -7, -8, -4,
    0, 0, -1, 0, 1, 0, 0, -13,
    -2, 6, 4, -4, -7, 0, 1, -6,
    0, -10, -1, -2, 4, -18, -2, 1,
    0, 0, -12, -2, -10, -2, -14, 0,
    0, -13, 0, 11, 1, 0, -1, 0,
    0, 0, 0, -1, -1, -7, -1, 0,
    0, 0, 0, 0, -6, 0, -2, 0,
    -1, -5, -9, 0, 0, -1, -3, -6,
    -2, 0, -1, 0, 0, 0, 0, -9,
    -2, -6, -6, -2, -3, -5, -2, -3,
    0, -4, -2, -6, -3, 0, -2, -4,
    -2, -4, 0, 1, 0, -1, -6, 0,
    0, -3, 0, 0, 0, 0, 2, 0,
    1, -4, 8, 0, -2, -2, -2, 0,
    0, 0, 0, 0, 0, -6, 0, -2,
    0, -3, -2, 0, -4, -5, -6, -2,
    0, -4, 2, 8, 0, 0, 0, 0,
    15, 0, 0, 1, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -4,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, -2, -2, 0, 0, -4, -2, 0,
    0, -4, 0, 3, -1, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    4, 2, -2, 0, -6, -3, 0, 6,
    -6, -6, -4, -4, 8, 3, 2, -17,
    -1, 4, -2, 0, -2, 2, -2, -7,
    0, -2, 2, -2, -2, -6, -2, 0,
    0, 6, 4, 0, -5, 0, -11, -2,
    6, -2, -7, 1, -2, -6, -6, -2,
    2, 0, -3, 0, -5, 0, 2, 6,
    -4, -7, -8, -5, 6, 0, 1, -14,
    -2, 2, -3, -1, -4, 0, -4, -7,
    -3, -3, -2, 0, 0, -4, -4, -2,
    0, 6, 4, -2, -11, 0, -11, -3,
    0, -7, -11, -1, -6, -3, -6, -5,
    0, 0, -2, 0, -4, -2, 0, -2,
    -3, 0, 3, -6, 2, 0, 0, -10,
    0, -2, -4, -3, -1, -6, -5, -6,
    -4, 0, -6, -2, -4, -4, -6, -2,
    0, 0, 1, 9, -3, 0, -6, -2,
    0, -2, -4, -4, -5, -5, -7, -2,
    4, 0, -3, 0, -10, -2, 1, 4,
    -6, -7, -4, -6, 6, -2, 1, -18,
    -3, 4, -4, -3, -7, 0, -6, -8,
    -2, -2, -2, -2, -4, -6, -1, 0,
    0, 6, 5, -1, -12, 0, -12, -4,
    5, -7, -13, -4, -7, -8, -10, -6,
    0, 0, 0, 0, -2, 0, 0, 2,
    -2, 4, 1, -4, 4, 0, 0, -6,
    -1, 0, -1, 0, 1, 1, -2, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 2, 6, 0, 0, -2, 0,
    0, 0, 0, -1, -1, -2, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 7, 0, 3, 1, 1, -2,
    0, 4, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 6, 0, 5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, -2, 3, 0, 6, 0,
    0, 19, 2, -4, -4, 2, 2, -1,
    1, -10, 0, 0, 9, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 7, 27, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, -5, 0, 0, 1, 0,
    0, 2, 25, -4, -2, 6, 5, -5,
    2, 0, 0, 2, 2, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -5, 0, 0, 0, -5,
    0, 0, 0, 0, -4, -1, 0, 0,
    0, -4, 0, -2, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, 0, -5, 0, 0, 0, -3,
    2, -2, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -9, 0, -2,
    0, 0, -16, -4, -8, -2, -7, 0,
    0, -13, 0, -5, -1, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, -2,
    0, 0, 0, 0, -4, 0, -4, 2,
    -2, 4, 0, -1, -4, -1, -3, -4,
    0, -2, -1, -1, 1, -5, -1, 0,
    0, 0, -17, -2, -3, 0, -4, 0,
    -1, -9, -2, 0, 0, -1, -2, 0,
    0, 0, 0, 1, 0, -1, -3, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, -4, 0, -1, 0, 0, 0, -4,
    2, 0, 0, 0, -5, -2, -4, 0,
    0, -5, 0, -2, 0, -9, 0, 0,
    0, 0, -19, 0, -4, -7, -10, 0,
    0, -13, 0, -1, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -3, -1,
    1, 0, 0, 3, -2, 0, 6, 9,
    -2, -2, -6, 2, 9, 3, 4, -5,
    2, 8, 2, 6, 4, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 9, -3, -2, 0, -2, 15,
    8, 15, 0, 0, 0, 2, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -16, -2, -2, -8, -9, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -16, -2, -2, -8, -9, 0,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -4, 2, 0, -2, 2, 3, 2, -6,
    0, 0, -2, 2, 0, 2, 0, 0,
    0, 0, -5, 0, -2, -1, -4, 0,
    -2, -8, 0, 12, -2, 0, -4, -1,
    0, -1, -3, 0, -2, -5, -4, -2,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, -16, -2, -2, -8, -9, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -6, -2, -2, 6,
    -2, -2, -8, 1, -1, 1, -1, -5,
    0, 4, 0, 2, 1, 2, -5, -8,
    -2, 0, -7, -4, -5, -8, -7, 0,
    -3, -4, -2, -2, -2, -1, -2, -1,
    0, -1, -1, 3, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -2, -2, 0,
    0, -5, 0, -1, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, 0, 0, 0, -2, 0, 0, -3,
    -2, 2, 0, -3, -4, -1, 0, -6,
    -1, -4, -1, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, 6, 0, 0, -3, 0,
    0, 0, 0, -2, 0, -2, 0, 0,
    0, 0, -1, 0, -4, 0, 0, 8,
    -2, -6, -6, 1, 2, 2, 0, -5,
    1, 3, 1, 6, 1, 6, -1, -5,
    0, 0, -8, 0, 0, -6, -5, 0,
    0, -4, 0, -2, -3, 0, -3, 0,
    -3, 0, -1, 3, 0, -2, -6, -2,
    0, 0, -2, 0, -4, 0, 0, 2,
    -4, 0, 2, -2, 2, 0, 0, -6,
    0, -1, -1, 0, -2, 2, -2, 0,
    0, 0, -8, -2, -4, 0, -6, 0,
    0, -9, 0, 7, -2, 0, -3, 0,
    1, 0, -2, 0, -2, -6, 0, -2,
    0, 0, 0, 0, -1, 0, 0, 2,
    -2, 1, 0, 0, -2, -1, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 4, 0, 0, -2, 0,
    0, 0, 0, 0, 0, -2, -2, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 3,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserratMedium_12 = {
#else
lv_font_t lv_font_montserratMedium_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 12,          /*The maximum line height required by the font  default: (f.src.ascent - f.src.descent)*/
    .base_line = 2,                          /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_12*/

