
/**
 * @file main.c
 * @brief ESP32主程序 - WiFi和OneNET云平台通信示例
 * @version 1.0
 * @date 2024-08-18
 *
 * @details
 * 本程序演示了ESP32连接WiFi网络并与OneNET云平台进行通信的完整流程。
 * 主要功能：
 * 1. 连接WiFi网络
 * 2. 连接OneNET MQTT服务器
 * 3. 定时上报设备数据（温度、湿度等）
 * 4. 接收并处理云端下发的命令
 * 5. 自动重连和错误处理
 *
 * @note 使用前请在wifi_onenet.h中配置正确的WiFi和OneNET参数
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "esp_err.h"
#include "esp_log.h"

/* BSP related header files */
#include "led.h"
#include "iic.h"
#include "xl9555.h"
#include "adc1.h"

/* WiFi和OneNET模块头文件 */
#include "wifi_onenet.h"

/* LVGL相关头文件 */
#include "lvgl.h"
#include "lv_port_disp.h"
#include "lv_port_indev.h"
#include "gui_guider.h"

/* ==================== 全局变量定义 ==================== */
static const char *TAG = "MAIN";

i2c_obj_t i2c0_master;

/* LVGL相关变量 */
lv_ui guider_ui;
static esp_timer_handle_t lvgl_tick_timer = NULL;

/* 设备状态变量 */
static bool device_online = false;
static int led_state = 0;  /* LED状态：0=关闭，1=打开 */

/* ==================== LVGL相关函数 ==================== */

static void lv_tick_task(void *arg)
{
    lv_tick_inc(1);
}

void lvgl_tick_timer_init(void)
{
    const esp_timer_create_args_t timer_args = {
        .callback = &lv_tick_task,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "lv_tick_timer"
    };

    esp_timer_create(&timer_args, &lvgl_tick_timer);
    esp_timer_start_periodic(lvgl_tick_timer, 1000);
}

/**
 * @brief 更新LVGL界面中的ADC数值显示
 * @param voltage ADC电压值
 */
void update_adc_display(float voltage)
{
    static char voltage_str[16];

    /* 格式化电压值为字符串，保留1位小数 */
    snprintf(voltage_str, sizeof(voltage_str), "%.1f", voltage);

    /* 获取spangroup中的第一个span并更新文本 */
    lv_span_t *span = lv_spangroup_get_child(guider_ui.screen_spangroup_1, 0);
    if (span != NULL) {
        lv_span_set_text(span, voltage_str);
        lv_spangroup_refr_mode(guider_ui.screen_spangroup_1);
    }
}

/**
 * @brief 更新LVGL界面中的波形图数据
 * @param voltage ADC电压值 (0-3.3V)
 */
void update_chart_data(float voltage)
{
    static uint16_t data_index = 0;
    static bool chart_full = false;

    /* 确保电压值在有效范围内 (0-3.3V) */
    if (voltage < 0.0f) voltage = 0.0f;
    if (voltage > 3.3f) voltage = 3.3f;

    /* 将电压值直接映射到图表坐标值 (0-3.3V 对应 0-3.3) */
    /* 图表Y轴范围是0-4，所以3.3V的数据可以正常显示 */
    lv_coord_t chart_value = (lv_coord_t)(voltage * 10);  /* 0-3.3V -> 0-33，图表内部会除以10显示为0-3.3 */

    /* 如果图表已满，使用滚动模式 */
    if (chart_full) {
        /* 获取当前数据数组 */
        lv_coord_t *data_array = lv_chart_get_y_array(guider_ui.screen_chart_1, guider_ui.screen_chart_1_0);
        if (data_array != NULL) {
            /* 向左移动所有数据点 */
            for (int i = 0; i < 18; i++) {
                data_array[i] = data_array[i + 1];
            }
            /* 在最后位置添加新数据 */
            data_array[18] = chart_value;
            /* 通知图表数据已更新 */
            lv_chart_refresh(guider_ui.screen_chart_1);
        }
    } else {
        /* 图表未满，直接添加数据 */
        lv_chart_set_next_value(guider_ui.screen_chart_1, guider_ui.screen_chart_1_0, chart_value);
        data_index++;
        if (data_index >= 19) {
            chart_full = true;
        }
    }
}

/* ==================== WiFi和OneNET回调函数 ==================== */

/**
 * @brief WiFi连接状态变化回调函数
 * @param connected WiFi连接状态，true表示已连接，false表示已断开
 *
 * @details
 * 当WiFi连接状态发生变化时，此函数会被自动调用。
 * 连接成功时启动OneNET MQTT服务和数据上报任务。
 * 连接断开时停止相关服务以节省资源。
 */
static void wifi_status_callback(bool connected)
{
    if (connected) {
        ESP_LOGI(TAG, "WiFi connected! Starting OneNET service...");
        device_online = true;

        /* Start OneNET MQTT connection after WiFi connected */
        esp_err_t ret = onenet_mqtt_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "OneNET MQTT started successfully");

            /* Set auto report interval to 3 seconds */
            ret = onenet_set_auto_report_interval(3000);
            if (ret == ESP_OK) {
                ESP_LOGI(TAG, "Auto report interval set successfully (3s)");
            } else {
                ESP_LOGE(TAG, "Failed to set auto report interval");
            }
        } else {
            ESP_LOGE(TAG, "Failed to start OneNET MQTT");
        }

     

    } else {
        ESP_LOGW(TAG, "WiFi disconnected! Stopping OneNET service...");
        device_online = false;

        /* Stop related services when WiFi disconnected */
        onenet_set_auto_report_interval(0);  /* Disable auto report */
        onenet_mqtt_stop();

        /* LED indicator control can be added here */
        // led_off();  // Turn off LED to indicate offline

        ESP_LOGI(TAG, "System will automatically retry WiFi connection...");
    }
}

/**
 * @brief OneNET命令接收回调函数
 * @param topic 接收到的MQTT主题
 * @param data 接收到的数据内容
 * @param data_len 数据长度
 *
 * @details
 * 当从OneNET平台接收到命令时，此函数会被自动调用。
 * 可以在此函数中解析JSON命令并执行相应的设备控制操作。
 */
static void onenet_cmd_callback(const char* topic, const char* data, int data_len)
{
    ESP_LOGI(TAG, "📨 收到OneNET平台命令");
    ESP_LOGI(TAG, "主题: %s", topic);
    ESP_LOGI(TAG, "数据: %.*s", data_len, data);

    /* 解析JSON命令示例 */
    char data_buf[256];
    if (data_len < sizeof(data_buf)) {
        snprintf(data_buf, sizeof(data_buf), "%.*s", data_len, data);

        /* 示例：LED控制命令 */
        if (strstr(data_buf, "\"led\"") != NULL) {
            if (strstr(data_buf, "\"on\"") != NULL || strstr(data_buf, "\"1\"") != NULL) {
                ESP_LOGI(TAG, "💡 执行命令：打开LED");
                led_state = 1;
                // led_on();  // 取消注释以实际控制LED

                /* 上报LED状态到OneNET */
                onenet_report_property("led_status", "on");

            } else if (strstr(data_buf, "\"off\"") != NULL || strstr(data_buf, "\"0\"") != NULL) {
                ESP_LOGI(TAG, "💡 执行命令：关闭LED");
                led_state = 0;
                // led_off();  // 取消注释以实际控制LED

                /* 上报LED状态到OneNET */
                onenet_report_property("led_status", "off");
            }
        }

        /* 示例：设备重启命令 */
        if (strstr(data_buf, "\"reboot\"") != NULL) {
            ESP_LOGW(TAG, "🔄 执行命令：设备重启");
            ESP_LOGW(TAG, "设备将在3秒后重启...");
            vTaskDelay(pdMS_TO_TICKS(3000));
            esp_restart();
        }

        /* 示例：获取设备状态命令 */
        if (strstr(data_buf, "\"get_status\"") != NULL) {
            ESP_LOGI(TAG, "📊 执行命令：获取设备状态");

            /* 构造设备状态数据 */
            char status_params[256];
            snprintf(status_params, sizeof(status_params),
                "{"
                "\"device_online\":{\"value\":\"%s\"},"
                "\"led_status\":{\"value\":\"%s\"},"
                "\"free_heap\":{\"value\":%ld},"
                "\"uptime\":{\"value\":%lld}"
                "}",
                device_online ? "true" : "false",
                led_state ? "on" : "off",
                esp_get_free_heap_size(),
                esp_timer_get_time() / 1000000  // 转换为秒
            );

            /* 上报设备状态 */
            onenet_report_properties(status_params);
        }
    }
}

/* ==================== 裸机版本 - 无需FreeRTOS任务 ==================== */
/*
 * 原来的手动数据上报任务和系统监控任务已经集成到主循环中
 * 使用裸机方式实现，无需创建额外的FreeRTOS任务
 */

/**
 * @brief 程序入口函数
 * @details
 * ESP32应用程序的主入口点，完成系统初始化和启动各种服务。
 *
 * 初始化流程：
 * 1. 初始化NVS存储
 * 2. 初始化硬件外设（I2C、XL9555等）
 * 3. 初始化WiFi和OneNET服务
 * 4. 连接WiFi网络
 * 5. 创建应用任务
 * 6. 进入主循环
 */
void app_main(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "🚀 ESP32 WiFi OneNET 应用程序启动");
    ESP_LOGI(TAG, "📋 固件版本: v1.0.0");
    ESP_LOGI(TAG, "🔧 编译时间: %s %s", __DATE__, __TIME__);

    /* ==================== 基础系统初始化 ==================== */

    /* 初始化NVS存储 */
    ESP_LOGI(TAG, "🔧 正在初始化NVS存储...");
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "⚠️  NVS存储需要擦除，正在重新初始化...");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "✅ NVS存储初始化完成");

    /* ==================== 硬件外设初始化 ==================== */

    /* Initialize hardware peripherals */
    ESP_LOGI(TAG, "Initializing hardware peripherals...");
    led_init();                                                 /* Initialize LED */
    i2c0_master = iic_init(I2C_NUM_0);                          /* Initialize IIC0 */
    xl9555_init(i2c0_master);                                   /* Initialize XL9555 */
    adc_init();                                                 /* Initialize ADC */
    ESP_LOGI(TAG, "Hardware peripherals initialized successfully");

    /* ==================== LVGL显示初始化 ==================== */
    ESP_LOGI(TAG, "🔧 正在初始化LVGL显示系统...");
    lvgl_tick_timer_init();
    lv_init();
    lv_port_disp_init();
    lv_port_indev_init();
    setup_ui(&guider_ui);
    ESP_LOGI(TAG, "✅ LVGL显示系统初始化完成");

    /* ==================== WiFi和OneNET初始化 ==================== */

    ESP_LOGI(TAG, "🔧 正在初始化WiFi和OneNET服务...");
    ret = wifi_onenet_init(wifi_status_callback, onenet_cmd_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "❌ WiFi和OneNET初始化失败！");
        ESP_LOGE(TAG, "请检查配置参数并重新编译");
        return;
    }
    ESP_LOGI(TAG, "✅ WiFi和OneNET服务初始化完成");

    /* 连接WiFi网络 */
    ESP_LOGI(TAG, "🔧 正在连接WiFi网络...");
    ret = wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "❌ WiFi连接失败！");
        ESP_LOGE(TAG, "请检查WiFi配置参数");
        /* 即使WiFi连接失败，程序也继续运行，系统会自动重试 */
    }

    /* ==================== 裸机版本初始化 ==================== */

    /* 裸机版本不需要创建任务，所有功能在主循环中处理 */
    ESP_LOGI(TAG, "✅ 裸机版本初始化完成，所有功能将在主循环中处理");

    /* ==================== 主循环（裸机版本） ==================== */

    ESP_LOGI(TAG, "System initialization completed, entering main loop (bare metal version)");
    ESP_LOGI(TAG, "System features:");
    ESP_LOGI(TAG, "   - WiFi auto connection and reconnection");
    ESP_LOGI(TAG, "   - OneNET MQTT communication");
    ESP_LOGI(TAG, "   - Real ADC data collection and display");
    ESP_LOGI(TAG, "   - Auto ADC data reporting (10s interval)");
    ESP_LOGI(TAG, "   - Manual ADC data reporting (5s interval)");
    ESP_LOGI(TAG, "   - Cloud command reception and processing");

    /* LVGL主循环 */

    /* Main loop variables for bare metal version */
    uint16_t adcdata;
    float voltage = 0.0f;  /* Initialize voltage to avoid uninitialized warning */
    static int64_t last_adc_update = 0;
    static int64_t last_gui_update = 0;

    /* Main loop */
    while (1) {
        /* LVGL task handler */
        lv_task_handler();

        onenet_process();
        int64_t current_time = esp_timer_get_time() / 1000;  /* Current time in milliseconds */

        /* Update ADC data every 100ms */
        if ((current_time - last_adc_update) >= 100) {
            /* ADC sampling using the new function */
            voltage = adc_get_voltage(ADC_ADCX_CHY, 20);            /* Get voltage directly */
            adcdata = adc_get_result_average(ADC_ADCX_CHY, 20);     /* Get raw value for logging */

            /* Update ADC data in OneNET module */
            onenet_update_adc_data(voltage);

            /* Print ADC data to console for debugging */
            ESP_LOGD(TAG, "ADC: raw=%d, voltage=%.1fV", adcdata, voltage);

            LED_TOGGLE();                                           /* Toggle LED */
            last_adc_update = current_time;
        }

        /* Update LVGL GUI with ADC data every 500ms (slower update for better visualization) */
        if ((current_time - last_gui_update) >= 500) {
            update_adc_display(voltage);
            update_chart_data(voltage);

            /* Debug: Print GUI update info */
            ESP_LOGI(TAG, "GUI Update: voltage=%.2fV, chart_value=%d", voltage, (int)(voltage * 100));

            last_gui_update = current_time;
        }

        /* Manual ADC data reporting removed - using auto report only */

        /* ==================== Main loop delay ==================== */
        /* Delay 50ms to give system some processing time */
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}
