/**
 * @file wifi_onenet.h
 * @brief ESP32 WiFi连接和OneNET云平台通信模块头文件
 * @version 1.0
 * @date 2024-08-18
 *
 * @details
 * 本模块实现了ESP32连接WiFi网络并与中国移动OneNET云平台进行MQTT通信的功能。
 * 主要功能包括：
 * 1. WiFi网络连接管理
 * 2. OneNET MQTT客户端连接
 * 3. 设备属性数据上报
 * 4. 云端命令接收和处理
 * 5. 自动重连机制
 *
 * @note 使用前请修改配置参数：
 * - WIFI_SSID: 您的WiFi网络名称
 * - WIFI_PASSWORD: 您的WiFi密码
 * - ONENET_PRODUCT_ID: OneNET平台的产品ID
 * - ONENET_DEVICE_ID: OneNET平台的设备ID
 * - ONENET_DEVICE_TOKEN: OneNET平台生成的设备Token
 *
 * <AUTHOR>
 */

#ifndef __WIFI_ONENET_H
#define __WIFI_ONENET_H

/* ==================== 系统头文件 ==================== */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stddef.h>
#include <inttypes.h>

/* ==================== ESP-IDF头文件 ==================== */
#include "esp_wifi.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "esp_log.h"
#include "esp_err.h"

/* ==================== 系统头文件 ==================== */
#include "esp_timer.h"

/* ==================== 网络相关头文件 ==================== */
#include "lwip/sockets.h"
#include "lwip/dns.h"
#include "lwip/netdb.h"
#include "mqtt_client.h"

/* ==================== WiFi配置参数 ==================== */
/**
 * @brief WiFi网络名称（SSID）
 * @warning 请修改为您的实际WiFi网络名称
 */
#define WIFI_SSID               "OneNet"

/**
 * @brief WiFi网络密码
 * @warning 请修改为您的实际WiFi密码
 */
#define WIFI_PASSWORD           "123456789"

/**
 * @brief WiFi连接最大重试次数
 * @details 当WiFi连接失败时，系统会自动重试连接，最多重试5次
 */
#define WIFI_MAXIMUM_RETRY      5

/* ==================== OneNET云平台配置参数 ==================== */
/**
 * @brief OneNET产品ID
 * @details 在OneNET平台创建产品后获得的唯一标识符
 * @warning 请修改为您在OneNET平台创建的实际产品ID
 */
#define ONENET_PRODUCT_ID       "ubN5T6u660"

/**
 * @brief OneNET设备ID（设备名称）
 * @details 在OneNET平台创建设备后设置的设备名称
 * @warning 请修改为您在OneNET平台创建的实际设备ID
 */
#define ONENET_DEVICE_ID        "ESP32S3"

/**
 * @brief OneNET设备Token
 * @details 设备连接OneNET平台的认证凭证，由OneNET平台生成
 * @warning 请修改为您在OneNET平台生成的实际Token
 * @note Token生成方法：
 *       1. 登录OneNET平台 (https://open.iot.10086.cn/)
 *       2. 进入设备管理页面
 *       3. 选择您的设备，点击"生成Token"
 *       4. 复制生成的Token字符串替换此处的值
 */
#define ONENET_DEVICE_TOKEN     "version=2018-10-31&res=products%2FubN5T6u660%2Fdevices%2FESP32S3&et=1853538616&method=md5&sign=AU1PrC1GvHccF2rg9f7%2FYA%3D%3D"

/**
 * @brief OneNET MQTT服务器地址
 * @details OneNET平台官方MQTT服务器域名，通常不需要修改
 */
#define ONENET_MQTT_HOST        "mqtts.heclouds.com"

/**
 * @brief OneNET MQTT服务器端口
 * @details OneNET平台官方MQTT服务器端口，通常不需要修改
 */
#define ONENET_MQTT_PORT        1883

/* ==================== OneNET MQTT主题定义 ==================== */
/**
 * @brief 设备属性上报主题
 * @details 设备向OneNET平台上报属性数据时使用的MQTT主题
 */
#define ONENET_TOPIC_PROP_POST          "$sys/" ONENET_PRODUCT_ID "/" ONENET_DEVICE_ID "/thing/property/post"

/**
 * @brief 设备属性设置主题
 * @details OneNET平台向设备下发属性设置命令时使用的MQTT主题
 */
#define ONENET_TOPIC_PROP_SET           "$sys/" ONENET_PRODUCT_ID "/" ONENET_DEVICE_ID "/thing/property/set"

/**
 * @brief 设备属性上报回复主题
 * @details OneNET平台对设备属性上报的回复主题
 */
#define ONENET_TOPIC_PROP_POST_REPLY    "$sys/" ONENET_PRODUCT_ID "/" ONENET_DEVICE_ID "/thing/property/post/reply"

/**
 * @brief 设备属性设置回复主题
 * @details 设备对OneNET平台属性设置命令的回复主题
 */
#define ONENET_TOPIC_PROP_SET_REPLY     "$sys/" ONENET_PRODUCT_ID "/" ONENET_DEVICE_ID "/thing/property/set/reply"

/* ==================== OneNET消息格式定义 ==================== */
/**
 * @brief OneNET消息体格式模板
 * @details 符合OneNET标准的JSON消息格式（简化版本，不包含version字段）
 * @param %s 消息ID（用户自定义的字符串）
 * @param %s 参数内容（包含具体的属性数据，不包含外层params结构）
 */
#define ONENET_BODY_FORMAT      "{\"id\":\"%s\",\"params\":{%s}}"

/* ==================== 状态定义 ==================== */
/**
 * @brief WiFi连接状态
 */
typedef enum {
    WIFI_STATE_DISCONNECTED = 0,       /**< WiFi未连接 */
    WIFI_STATE_CONNECTING,              /**< WiFi连接中 */
    WIFI_STATE_CONNECTED,               /**< WiFi已连接 */
    WIFI_STATE_FAILED,                  /**< WiFi连接失败 */
} wifi_state_t;

/* ==================== 数据类型定义 ==================== */
/**
 * @brief MQTT客户端状态枚举
 * @details 定义MQTT客户端的各种连接状态
 */
typedef enum {
    MQTT_STATE_ERROR = -1,          /**< MQTT连接错误状态 */
    MQTT_STATE_UNKNOWN = 0,         /**< MQTT状态未知 */
    MQTT_STATE_INIT,                /**< MQTT已初始化但未连接 */
    MQTT_STATE_CONNECTED,           /**< MQTT已连接 */
    MQTT_STATE_DISCONNECTED,        /**< MQTT已断开连接 */
} mqtt_client_state_t;

/**
 * @brief WiFi连接状态回调函数类型
 * @param connected WiFi连接状态，true表示已连接，false表示已断开
 * @details 当WiFi连接状态发生变化时，系统会调用此回调函数通知应用程序
 */
typedef void (*wifi_status_callback_t)(bool connected);

/**
 * @brief OneNET命令接收回调函数类型
 * @param topic 接收到的MQTT主题
 * @param data 接收到的数据内容
 * @param data_len 数据长度
 * @details 当从OneNET平台接收到命令时，系统会调用此回调函数通知应用程序
 */
typedef void (*onenet_cmd_callback_t)(const char* topic, const char* data, int data_len);

/* ==================== 函数声明 ==================== */

/**
 * @brief WiFi网络初始化
 * @param callback WiFi状态变化回调函数，当WiFi连接状态改变时会调用此函数
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @details
 * 此函数完成以下操作：
 * 1. 初始化网络接口
 * 2. 创建默认WiFi STA接口
 * 3. 配置WiFi参数（SSID、密码等）
 * 4. 注册WiFi事件处理函数
 * 5. 启动WiFi服务
 *
 * @note 调用此函数前需要先调用nvs_flash_init()初始化NVS
 * @warning 请确保在wifi_onenet.h中正确配置了WIFI_SSID和WIFI_PASSWORD
 */
esp_err_t wifi_init(wifi_status_callback_t callback);

/**
 * @brief 连接WiFi网络
 * @return esp_err_t
 *         - ESP_OK: 连接成功
 *         - ESP_FAIL: 连接失败
 * @details
 * 此函数会阻塞等待WiFi连接结果，直到连接成功或达到最大重试次数。
 * 连接过程中会自动处理重连逻辑。
 *
 * @note 调用此函数前必须先调用wifi_init()进行初始化
 */
esp_err_t wifi_connect(void);

/**
 * @brief 断开WiFi网络连接
 * @return esp_err_t
 *         - ESP_OK: 断开成功
 *         - ESP_FAIL: 断开失败
 * @details 主动断开当前的WiFi连接
 */
esp_err_t wifi_disconnect(void);

/**
 * @brief 获取WiFi连接状态
 * @return true WiFi已连接
 * @return false WiFi未连接
 * @details 返回当前WiFi的连接状态，可用于判断网络是否可用
 */
bool wifi_is_connected(void);

/**
 * @brief OneNET MQTT客户端初始化
 * @param cmd_callback 命令接收回调函数，当接收到OneNET平台下发的命令时会调用此函数
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @details
 * 此函数完成以下操作：
 * 1. 配置MQTT连接参数（服务器地址、端口、认证信息等）
 * 2. 创建MQTT客户端实例
 * 3. 注册MQTT事件处理函数
 * 4. 设置命令接收回调函数
 *
 * @note 调用此函数前请确保已正确配置OneNET相关参数
 * @warning 请确保在wifi_onenet.h中正确配置了OneNET平台参数
 */
esp_err_t onenet_mqtt_init(onenet_cmd_callback_t cmd_callback);

/**
 * @brief 启动OneNET MQTT连接
 * @return esp_err_t
 *         - ESP_OK: 启动成功
 *         - ESP_FAIL: 启动失败
 * @details
 * 启动MQTT客户端，开始连接OneNET平台。连接成功后会自动订阅相关主题。
 *
 * @note 调用此函数前必须先调用onenet_mqtt_init()进行初始化
 * @note 建议在WiFi连接成功后再调用此函数
 */
esp_err_t onenet_mqtt_start(void);

/**
 * @brief 停止OneNET MQTT连接
 * @return esp_err_t
 *         - ESP_OK: 停止成功
 *         - ESP_FAIL: 停止失败
 * @details 断开与OneNET平台的MQTT连接
 */
esp_err_t onenet_mqtt_stop(void);

/**
 * @brief 获取MQTT客户端连接状态
 * @return mqtt_client_state_t MQTT客户端当前状态
 *         - MQTT_STATE_UNKNOWN: 状态未知
 *         - MQTT_STATE_INIT: 已初始化但未连接
 *         - MQTT_STATE_CONNECTED: 已连接
 *         - MQTT_STATE_DISCONNECTED: 已断开连接
 *         - MQTT_STATE_ERROR: 连接错误
 * @details 返回当前MQTT客户端的连接状态
 */
mqtt_client_state_t onenet_mqtt_get_state(void);

/**
 * @brief 上报单个设备属性到OneNET平台
 * @param property_name 属性名称，必须与OneNET平台物模型中定义的属性名一致
 * @param value 属性值，以字符串形式传入
 * @return int 消息ID，成功时返回正数，失败时返回-1
 * @details
 * 向OneNET平台上报单个设备属性数据。函数会自动构造符合OneNET物模型规范的JSON消息。
 *
 * @note 调用此函数前请确保MQTT已连接到OneNET平台
 * @warning 属性名称必须与OneNET平台物模型中定义的属性名完全一致
 *
 * @code
 * // 示例：上报温度数据
 * int msg_id = onenet_report_property("temperature", "25.6");
 * if (msg_id != -1) {
 *     ESP_LOGI(TAG, "Temperature reported, msg_id=%d", msg_id);
 * }
 * @endcode
 */
int onenet_report_property(const char* property_name, const char* value);

/**
 * @brief 上报多个设备属性到OneNET平台
 * @param params JSON格式的参数字符串，包含多个属性数据
 * @return int 消息ID，成功时返回正数，失败时返回-1
 * @details
 * 向OneNET平台上报多个设备属性数据。参数必须是符合OneNET物模型规范的JSON格式。
 *
 * @note 调用此函数前请确保MQTT已连接到OneNET平台
 * @warning JSON格式必须符合OneNET物模型规范
 *
 * @code
 * // 示例：同时上报温度和湿度数据
 * char params[] = "{\"temperature\":{\"value\":25.6},\"humidity\":{\"value\":60.5}}";
 * int msg_id = onenet_report_properties(params);
 * if (msg_id != -1) {
 *     ESP_LOGI(TAG, "Properties reported, msg_id=%d", msg_id);
 * }
 * @endcode
 */
int onenet_report_properties(const char* params);

/**
 * @brief 上报ADC数据到OneNET平台（便捷函数）
 * @param adc_value ADC电压值（浮点数）
 * @return int 消息ID，成功时返回正数，失败时返回-1
 * @details
 * 这是一个便捷函数，专门用于上报ADC数据。会自动格式化为保留一位小数的字符串。
 *
 * @note 调用此函数前请确保MQTT已连接到OneNET平台
 *
 * @code
 * // 示例：上报ADC电压值
 * float voltage = 3.14f;
 * int msg_id = onenet_report_adc(voltage);
 * if (msg_id != -1) {
 *     ESP_LOGI(TAG, "ADC reported: %.1fV, msg_id=%d", voltage, msg_id);
 * }
 * @endcode
 */
int onenet_report_adc(float adc_value);

/**
 * @brief 更新当前ADC数据值
 * @param adc_value 当前ADC电压值（浮点数）
 * @details
 * 更新内部存储的ADC数据值，用于自动上报。
 * 应该在主循环中定期调用此函数来更新最新的ADC采集值。
 *
 * @code
 * // 示例：更新ADC数据
 * float voltage = 2.5f;
 * onenet_update_adc_data(voltage);
 * @endcode
 */
void onenet_update_adc_data(float adc_value);

/**
 * @brief 构造OneNET数据包（类似OneNet_FillBuf函数）
 * @param buf 输出缓冲区，用于存储构造的数据包
 * @param buf_size 缓冲区大小
 * @return int 构造的数据包长度，失败时返回-1
 * @details
 * 这是一个便捷函数，用于构造复杂的OneNET数据包。
 * 可以根据需要添加多个属性数据。
 *
 * @note 此函数构造完整的OneNET消息格式，可直接用于MQTT发布
 *
 * @code
 * // 示例：构造包含多个属性的数据包
 * char buf[1024];
 * int len = onenet_fill_buf(buf, sizeof(buf));
 * if (len > 0) {
 *     // 直接发布buf到MQTT
 * }
 * @endcode
 */
int onenet_fill_buf(char *buf, size_t buf_size);

/**
 * @brief WiFi和OneNET完整初始化（一键初始化）
 * @param wifi_callback WiFi状态变化回调函数
 * @param cmd_callback OneNET命令接收回调函数
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @details
 * 这是一个便捷函数，一次性完成WiFi和OneNET的初始化工作，包括：
 * 1. WiFi网络初始化
 * 2. OneNET MQTT客户端初始化
 * 3. 设置相关回调函数
 *
 * @note 调用此函数后，还需要调用wifi_connect()连接WiFi网络
 * @note 建议在WiFi连接成功后再调用onenet_mqtt_start()启动MQTT连接
 *
 * @code
 * // 示例：完整初始化流程
 * esp_err_t ret = wifi_onenet_init(wifi_callback, cmd_callback);
 * if (ret == ESP_OK) {
 *     ret = wifi_connect();
 *     if (ret == ESP_OK) {
 *         ret = onenet_mqtt_start();
 *     }
 * }
 * @endcode
 */
esp_err_t wifi_onenet_init(wifi_status_callback_t wifi_callback, onenet_cmd_callback_t cmd_callback);

/**
 * @brief 设置自动数据上报间隔
 * @param report_interval_ms 数据上报间隔时间（毫秒），0表示禁用自动上报
 * @return esp_err_t
 *         - ESP_OK: 设置成功
 *         - ESP_FAIL: 设置失败
 * @details
 * 设置自动数据上报的时间间隔。需要在主循环中定期调用onenet_process()函数来处理上报。
 *
 * @note 设置为0可以禁用自动上报功能
 * @note 需要配合onenet_process()函数使用
 *
 * @code
 * // 示例：设置每5秒自动上报一次数据
 * onenet_set_auto_report_interval(5000);
 *
 * // 在主循环中调用
 * while(1) {
 *     onenet_process();
 *     vTaskDelay(pdMS_TO_TICKS(100));
 * }
 * @endcode
 */
esp_err_t onenet_set_auto_report_interval(uint32_t report_interval_ms);

/**
 * @brief OneNET处理函数（需要在主循环中定期调用）
 * @details
 * 此函数处理WiFi状态检查、MQTT连接维护、自动数据上报等功能。
 * 必须在主循环中定期调用，建议调用间隔不超过100ms。
 *
 * 处理内容：
 * 1. 检查WiFi连接状态
 * 2. 处理MQTT连接状态
 * 3. 执行自动数据上报
 * 4. 处理重连逻辑
 *
 * @note 此函数是非阻塞的，可以安全地在主循环中调用
 */
void onenet_process(void);

/**
 * @brief 获取WiFi连接状态
 * @return wifi_state_t 当前WiFi连接状态
 */
wifi_state_t wifi_get_state(void);

/* ==================== 便捷函数声明 ==================== */

/**
 * @brief 获取WiFi和OneNET状态信息（调试用）
 * @details
 * 打印当前WiFi连接状态和MQTT连接状态到串口，用于调试和状态监控。
 * 输出信息包括：
 * - WiFi连接状态
 * - MQTT连接状态
 * - 系统运行时间等
 */
void wifi_onenet_status_info(void);

/**
 * @brief WiFi和OneNET完整示例初始化（裸机版本）
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @details
 * 这是一个完整的示例函数，演示了如何使用本模块的裸机版本：
 * 1. 初始化WiFi和OneNET
 * 2. 连接WiFi网络
 * 3. 启动MQTT连接
 * 4. 设置自动数据上报间隔
 *
 * @note 调用此函数后，需要在主循环中定期调用onenet_process()
 * @note 调用此函数前请确保已正确配置WiFi和OneNET参数
 *
 * @code
 * // 使用示例
 * wifi_onenet_example_init();
 * while(1) {
 *     onenet_process();  // 处理WiFi和OneNET事务
 *     // 其他业务逻辑
 *     vTaskDelay(pdMS_TO_TICKS(100));
 * }
 * @endcode
 */
esp_err_t wifi_onenet_example_init(void);

#endif /* __WIFI_ONENET_H */
